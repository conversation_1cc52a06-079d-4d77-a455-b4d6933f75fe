defmodule LiteCrmWeb.LiveUserAuth do
  @moduledoc """
  Helpers for authenticating users in LiveViews.
  """

  use LiteCrmWeb, :verified_routes

  # For backward compatibility with existing code
  def on_mount(:live_user_optional, params, session, socket) do
    {status, socket} = LiteCrmWeb.UserAuth.on_mount(:mount_current_user, params, session, socket)
    socket = maybe_assign_admin_user(socket, session)
    {status, socket}
  end

  def on_mount(:live_user_required, params, session, socket) do
    {status, socket} =
      LiteCrmWeb.UserAuth.on_mount(:ensure_authenticated, params, session, socket)

    socket = maybe_assign_admin_user(socket, session)
    {status, socket}
  end

  def on_mount(:live_no_user, params, session, socket) do
    {status, socket} =
      LiteCrmWeb.UserAuth.on_mount(:redirect_if_user_is_authenticated, params, session, socket)

    socket = maybe_assign_admin_user(socket, session)
    {status, socket}
  end

  defp maybe_assign_admin_user(socket, session) do
    # The current_admin_user in the session could be a map or a struct
    # We need to handle both cases
    case session["current_admin_user"] do
      %{"id" => id} = admin_user when is_map(admin_user) ->
        # If it's a map (serialized from JSON), convert it to a proper User struct
        user = LiteCrm.Accounts.get_user!(id)
        Phoenix.Component.assign(socket, :current_admin_user, user)

      %LiteCrm.Accounts.User{} = admin_user ->
        # If it's already a User struct, use it directly
        Phoenix.Component.assign(socket, :current_admin_user, admin_user)

      _ ->
        # If there's no admin user in the session, don't assign anything
        socket
    end
  end
end
