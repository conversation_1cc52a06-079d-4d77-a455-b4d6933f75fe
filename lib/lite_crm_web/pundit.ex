defmodule LiteCrmWeb.Pundit do
  @moduledoc """
  Helpers for using Pundit in controllers and LiveViews.
  """

  @doc """
  Authorizes the given action for the given resource by the given user.
  If the action is not authorized, redirects to the given path with a flash message.
  """
  def authorize(conn_or_socket, action, resource, opts \\ [])

  def authorize(conn = %Plug.Conn{}, action, resource, opts) do
    user = conn.assigns.current_user

    if Pundit.can?(resource, user, action) do
      {:ok, conn}
    else
      error_message = opts[:error_message] || "You are not authorized to perform this action."
      redirect_path = opts[:redirect_path] || "/"

      {:error,
       conn
       |> Phoenix.Controller.put_flash(:error, error_message)
       |> Phoenix.Controller.redirect(to: redirect_path)}
    end
  end

  def authorize(socket = %Phoenix.LiveView.Socket{}, action, resource, opts) do
    user = socket.assigns.current_user

    if Pundit.can?(resource, user, action) do
      {:ok, socket}
    else
      error_message = opts[:error_message] || "You are not authorized to perform this action."
      redirect_path = opts[:redirect_path] || "/"

      {:error,
       socket
       |> Phoenix.LiveView.put_flash(:error, error_message)
       |> Phoenix.LiveView.redirect(to: redirect_path)}
    end
  end

  @doc """
  Authorizes the given action for the given resource by the given user.
  If the action is not authorized, raises an error.
  """
  def authorize!(conn_or_socket, action, resource)

  def authorize!(conn = %Plug.Conn{}, action, resource) do
    user = conn.assigns.current_user

    unless Pundit.can?(resource, user, action) do
      raise "Not authorized"
    end

    conn
  end

  def authorize!(socket = %Phoenix.LiveView.Socket{}, action, resource) do
    user = socket.assigns.current_user

    unless Pundit.can?(resource, user, action) do
      raise "Not authorized"
    end

    socket
  end
end
