defmodule LiteCrmWeb.Router do
  use LiteCrmWeb, :router
  import PhoenixStorybook.Router

  pipeline :browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {LiteCrmWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_user
  end

  pipeline :require_authenticated_user do
    plug :fetch_current_user
    plug :ensure_authenticated
  end

  defp ensure_authenticated(conn, _opts) do
    LiteCrmWeb.UserAuth.require_authenticated_user(conn, [])
  end

  pipeline :api do
    plug :accepts, ["json"]
  end

  scope "/" do
    storybook_assets()
  end

  scope "/", LiteCrmWeb do
    pipe_through(:browser)
    live_storybook("/storybook", backend_module: LiteCrmWeb.Storybook)
  end

  scope "/", LiteCrmWeb do
    pipe_through :browser

    get "/", RootController, :home
    get "/sign-in", UserSessionController, :new
    post "/sign-in", UserSessionController, :create
    get "/sign-up", UserRegistrationController, :new
    post "/sign-up", UserRegistrationController, :create
    delete "/sign-out", UserSessionController, :delete

    # Password reset routes
    get "/reset-password", UserResetPasswordController, :new
    post "/reset-password", UserResetPasswordController, :create
    get "/reset-password/:token", UserResetPasswordController, :edit
    put "/reset-password/:token", UserResetPasswordController, :update
  end

  # Routes that redirect to sign-in if not authenticated
  # or to dashboard if authenticated
  scope "/", LiteCrmWeb do
    pipe_through :browser

    live_session :redirect_authenticated,
      on_mount: [{LiteCrmWeb.UserAuth, :redirect_if_user_is_authenticated}, LiteCrmWeb.CurrentUrl] do
      live "/home", HomeLive, :show
    end
  end

  # Routes that require authentication
  scope "/", LiteCrmWeb do
    pipe_through [:browser, :require_authenticated_user]

    live_session :authenticated,
      on_mount: [{LiteCrmWeb.UserAuth, :ensure_authenticated}, LiteCrmWeb.CurrentUrl] do
      # Dashboard path
      live "/leads", LeadsLive
      live "/users", UsersLive
      live "/users/:id", UserLive
      live "/users/:id/roles", UserLive, :roles
      live "/dashboard", DashboardLive, :show
    end

    get "/users/:id/pdf", UserPdfController, :show
    get "/impersonate/:user_id", UserSessionController, :impersonate
    delete "/resume", UserSessionController, :resume
  end

  # Other scopes may use custom stacks.
  # scope "/api", LiteCrmWeb do
  #   pipe_through :api
  # end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:lite_crm, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through :browser

      live_dashboard "/dashboard", metrics: LiteCrmWeb.Telemetry
      forward "/mailbox", Plug.Swoosh.MailboxPreview
    end
  end

  defp fetch_current_user(conn, _opts) do
    LiteCrmWeb.UserAuth.fetch_current_user(conn, [])
  end
end
