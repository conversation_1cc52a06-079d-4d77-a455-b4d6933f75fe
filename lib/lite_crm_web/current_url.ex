defmodule LiteCrmWeb.CurrentUrl do
  use LiteCrmWeb, :live_view

  @moduledoc """
  Hook for storing current url into socket assigns.
  """

  def on_mount(:default, _params, _session, socket) do
    socket =
      attach_hook(socket, :set_current_url, :handle_params, fn
        _params, url, socket ->
          {:cont, assign(socket, current_url: URI.parse(url).path)}
      end)

    {:cont, socket}
  end
end
