defmodule LiteCrmWeb.MaterialComponents do
  @moduledoc """
  Provides Material components.
  """
  use Phoenix.Component
  use Gettext, backend: LiteCrmWeb.Gettext
  use LiteCrmWeb, :verified_routes
  alias Phoenix.LiveView.JS
  alias LiteCrmWeb.CoreComponents

  @doc """
  Material Modal.
  """
  attr :title, :string, required: true
  attr :id, :string, required: true
  attr :cancel_button, :string, default: "Cancel"
  attr :ok_button, :string, default: "Ok"
  attr :additional_classes, :string, default: nil
  attr :opened, :boolean, default: false
  slot :content

  def modal(assigns) do
    ~H"""
    <div class="mdc-dialog" id={@id} phx-hook="MaterialModal" data-opened={@opened}>
      <div class="mdc-dialog__container">
        <div
          class={["mdc-dialog__surface", @additional_classes]}
          role="alertdialog"
          aria-modal="true"
          aria-labelledby="my-dialog-title"
          aria-describedby="my-dialog-content"
        >
          <h2 class="mdc-dialog__title bg-secondary">
            <div class="!text-white flex items-center justify-between">
              <span>{@title}</span>
              <button
                class="mdc-icon-button material-icons mdc-dialog__close"
                data-mdc-dialog-action="close"
              >
                close
              </button>
            </div>
          </h2>
          <div class="mdc-dialog__content">
            {render_slot(@content)}
          </div>
          <div class="mdc-dialog__actions">
            <button
              type="button"
              class="mdc-button mdc-dialog__button mdc-dialog__close"
              data-mdc-dialog-action="close"
            >
              <div class="mdc-button__ripple"></div>
              <span class="mdc-button__label">{@cancel_button}</span>
            </button>
            <button type="button" class="mdc-button mdc-dialog__button" data-mdc-dialog-action="ok">
              <div class="mdc-button__ripple"></div>
              <span class="mdc-button__label">{@ok_button}</span>
            </button>
          </div>
        </div>
      </div>
      <div class="mdc-dialog__scrim"></div>
    </div>
    """
  end

  @doc """
  Material Modal for Form.
  """
  attr :title, :string, required: true
  attr :id, :string, required: true
  attr :form, :map, required: true
  attr :cancel_button, :string, default: "Cancel"
  attr :ok_button, :string, default: "Ok"
  attr :additional_classes, :string, default: nil
  attr :opened, :boolean, default: false
  attr :submit, :string, required: true
  attr :change, :string, required: true
  slot :content

  def form_modal(assigns) do
    ~H"""
    <div
      class={["mdc-dialog", @opened && "mdc-dialog--open"]}
      id={@id}
      phx-hook="MaterialModal"
      data-opened={@opened}
    >
      <div class="mdc-dialog__container">
        <.form
          :let={f}
          for={@form}
          phx-submit={@submit}
          phx-change={@change}
          novalidate="true"
          autocomplete="off"
        >
          <div
            class={["mdc-dialog__surface", @additional_classes]}
            role="alertdialog"
            aria-modal="true"
            aria-labelledby="my-dialog-title"
            aria-describedby="my-dialog-content"
          >
            <h2 class="mdc-dialog__title bg-secondary">
              <div class="!text-white flex items-center justify-between">
                <span>{@title}</span>
                <button
                  type="button"
                  class="mdc-icon-button material-icons mdc-dialog__close"
                  data-mdc-dialog-action="close"
                >
                  close
                </button>
              </div>
            </h2>
            <div class="mdc-dialog__content">
              {render_slot(@content, f)}
            </div>
            <div class="mdc-dialog__actions">
              <button
                type="button"
                class="mdc-button mdc-dialog__button mdc-dialog__close"
                data-mdc-dialog-action="close"
              >
                <div class="mdc-button__ripple"></div>
                <span class="mdc-button__label">{@cancel_button}</span>
              </button>
              <button type="submit" class="mdc-button mdc-dialog__button">
                <div class="mdc-button__ripple"></div>
                <span class="mdc-button__label">{@ok_button}</span>
              </button>
            </div>
          </div>
        </.form>
      </div>
      <div class="mdc-dialog__scrim"></div>
    </div>
    """
  end

  @doc """
  Material Aside.
  """
  attr :id, :string, required: true
  attr :disabled, :boolean, default: false
  slot :header
  slot :sidebar_menu

  def aside(assigns) do
    ~H"""
    <aside
      class={[
        "mdc-drawer mdc-drawer--dismissible !w-64 main-drawer !z-50",
        !@disabled && "mdc-drawer--open"
      ]}
      id={"#{@id}-aside"}
      phx-hook="MaterialAside"
    >
      <div class="mdc-drawer__content bg-slate-800 fixed top-0 !w-64">
        <header :if={@header != []} class="flex flex-row items-center pl-6 h-16 min-h-16 bg-primary">
          {render_slot(@header)}
        </header>
        {render_slot(@sidebar_menu)}
      </div>
    </aside>
    """
  end

  @doc """
  MDCTopAppBar https://github.com/material-components/material-components-web/tree/master/packages/mdc-top-app-bar
  """
  attr :id, :string, required: true
  attr :drawer_disabled, :boolean, default: false
  attr :current_user, :map
  attr :current_admin_user, :map, default: nil
  slot :heading

  def top_app_bar(assigns) do
    ~H"""
    <header class="mdc-top-app-bar app-bar !z-50" id={@id} phx-hook="MaterialTopAppBar">
      <%!-- <%= render partial: 'common/environment_banner', locals: { environment: ENV.fetch('APP_ENV') { Rails.env } } unless Rails.env.production? %> --%>
      <div class="mdc-top-app-bar__row !h-16 !min-h-16">
        <section class="mdc-top-app-bar__section mdc-top-app-bar__section--align-start">
          <span :if={@drawer_disabled}>LiteCRM</span>
          <button
            :if={!@drawer_disabled}
            class="material-icons mdc-top-app-bar__navigation-icon mdc-icon-button"
          >
            menu
          </button>
          <!--          <span class="mdc-top-app-bar__title">Dismissible Drawer</span>-->
        </section>
        <section
          class="mdc-top-app-bar__section mdc-top-app-bar__section--align-end"
          role="toolbar"
          id="top-app-bar"
        >
          <div class="user-actions">
            <%= if @current_user do %>
              <.menu_button id="user-menu-btn">
                <:button_content>{@current_user.email}</:button_content>
                <:menu_contents>
                  <ul
                    class="mdc-deprecated-list mdc-deprecated-list--icon-list"
                    role="menu"
                    aria-hidden="true"
                    aria-orientation="vertical"
                    tabindex="-1"
                  >
                    <a class="mdc-deprecated-list-item" role="menuitem">
                      <span class="mdc-deprecated-list-item__ripple"></span>
                      <span class="mdc-deprecated-list-item__icon material-icons">person</span>
                      <span class="mdc-deprecated-list-item__text pl-2">My Profile</span>
                    </a>
                    <%= if @current_admin_user do %>
                      <li class="mdc-list-divider" role="separator"></li>
                      <.link
                        href="/resume"
                        class="mdc-deprecated-list-item"
                        role="menuitem"
                        method="delete"
                      >
                        <span class="mdc-deprecated-list-item__ripple"></span>
                        <span class="mdc-deprecated-list-item__icon material-icons">key_off</span>
                        <span class="mdc-deprecated-list-item__text pl-2">
                          Return to Admin Account ({@current_admin_user.email})
                        </span>
                      </.link>
                    <% end %>
                    <li class="mdc-list-divider" role="separator"></li>
                    <.link
                      href={~p"/sign-out"}
                      method="delete"
                      class="mdc-deprecated-list-item"
                      role="menuitem"
                    >
                      <span class="mdc-deprecated-list-item__ripple"></span>
                      <span class="mdc-deprecated-list-item__icon material-icons">logout</span>
                      <span class="mdc-deprecated-list-item__text pl-2">Logout</span>
                    </.link>
                  </ul>
                </:menu_contents>
              </.menu_button>
            <% else %>
              <div class="mdc-touch-target-wrapper pr-4" data-controller="material-ripple">
                <a href={~p"/sign-in"} class="mdc-button mdc-button--touch menu-item">
                  <span class="mdc-button__ripple"></span>
                  <span class="mdc-button__touch"></span>
                  <span class="mdc-button__focus-ring"></span>
                  <span class="mdc-button__label uppercase text-white">sign in</span>
                </a>
              </div>
              <div class="mdc-touch-target-wrapper" data-controller="material-ripple">
                <a href={~p"/sign-up"} class="mdc-button mdc-button--touch menu-item">
                  <span class="mdc-button__ripple"></span>
                  <span class="mdc-button__touch"></span>
                  <span class="mdc-button__focus-ring"></span>
                  <span class="mdc-button__label uppercase text-white">sign up</span>
                </a>
              </div>
            <% end %>
          </div>
        </section>
      </div>
      {render_slot(@heading)}
    </header>
    """
  end

  @doc """
  Render Menu Button.
  """
  attr :id, :string, required: true
  slot :button_content
  slot :menu_contents

  def menu_button(assigns) do
    ~H"""
    <div class="!z-50" data-menu-button-logout-path-value="" id={@id} phx-hook="MaterialMenuButton">
      <button class="mdc-button mdc-button--touch menu-item menu-button">
        <span class="mdc-button__ripple"></span>
        <span class="mdc-button__focus-ring"></span>
        <span class="mdc-button__label flex items-center">
          <span class="hidden lg:inline">{render_slot(@button_content)}</span>
          <span class="material-icons">expand_more</span>
        </span>
      </button>
      <div class="mdc-menu mdc-menu-surface !w-auto">{render_slot(@menu_contents)}</div>
    </div>
    """
  end

  @doc """
  Sidebar menu
  """
  slot :link do
    attr :href, :string
    attr :icon, :string
    attr :current, :boolean
    attr :id, :string
  end

  def sidebar_menu(assigns) do
    ~H"""
    <div class="mdc-deprecated-list">
      <%= for link <- @link do %>
        <a
          href={link.href}
          class={["mdc-deprecated-list-item", link.current && "mdc-deprecated-list-item--activated"]}
          aria-current={if link.current, do: "page", else: "false"}
          phx-hook="MaterialRipple"
          id={link.id}
        >
          <span class="mdc-deprecated-list-item__ripple"></span>
          <i class="material-icons mdc-deprecated-list-item__graphic" aria-hidden="true">
            {link.icon}
          </i>
          <span class={["mdc-deprecated-list-item__text", link.current && "!font-bold"]}>
            {render_slot(link)}
          </span>
        </a>
      <% end %>
    </div>
    """
  end

  @doc """
  Tab Bar Menu
  """
  slot :link do
    attr :href, :string
    attr :icon, :string
    attr :current, :boolean
  end

  def tab_bar_menu(assigns) do
    ~H"""
    <nav class="mdc-tab-bar bg-white" role="tablist" phx-hook="MaterialTabBar" id="tab-bar">
      <div class="mdc-tab-scroller">
        <div class="mdc-tab-scroller__scroll-area">
          <div class="mdc-tab-scroller__scroll-content">
            <%= for link <- @link do %>
              <.link
                patch={link.href}
                class={["mdc-tab", link.current && "mdc-tab--active"]}
                role="tab"
                tabindex="0"
                aria-selected={if link.current, do: "true", else: "false"}
              >
                <span class="mdc-tab__content">
                  <span :if={link[:icon]} class="mdc-tab__icon material-icons" aria-hidden="true">
                    {link.icon}
                  </span>
                  <span class="mdc-tab__text-label">{render_slot(link)}</span>
                </span>
                <span class={[
                  "mdc-tab-indicator",
                  link.current && "mdc-tab-indicator--active"
                ]}>
                  <span class="mdc-tab-indicator__content mdc-tab-indicator__content--underline">
                  </span>
                </span>
                <span class="mdc-tab__ripple"></span>
                <div class="mdc-tab__focus-ring"></div>
              </.link>
            <% end %>
          </div>
        </div>
      </div>
    </nav>
    """
  end

  @doc """
  Material Input
  """
  attr :id, :string, required: true
  attr :label, :string, required: false
  attr :style, :atom, values: [:filled, :outlined], default: :filled
  attr :custom_css, :string, default: ""
  attr :provide_hidden, :boolean, default: false
  attr :helper, :string, default: nil

  attr :rest, :global,
    include: ~w(accept autocomplete capture cols disabled form list max maxlength min minlength
                multiple pattern placeholder readonly required rows size step)

  attr :field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, for example: @form[:email]"

  def material_input(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns =
      assigns
      |> assign(field: nil, id: assigns.id || field.id)
      |> assign(:errors, Enum.map(field.errors, &LiteCrmWeb.CoreComponents.translate_error(&1)))
      |> assign_new(:name, fn -> field.name end)
      |> assign_new(:value, fn -> field.value end)

    ~H"""
    <div phx-feedback-for={@name}>
      <label
        class={[
          "mdc-text-field w-full rounded-none",
          @style == :filled && "mdc-text-field--filled",
          @style == :outlined && "mdc-text-field--outlined",
          @value && "mdc-text-field--label-floating",
          Enum.count(@errors) > 0 && "mdc-text-field--invalid",
          @custom_css
        ]}
        phx-hook="MaterialTextField"
        id={"#{@id}-container"}
      >
        <%= if @style == :filled do %>
          <span class="mdc-text-field__ripple"></span>
          <span
            class={["mdc-floating-label", @value && "mdc-floating-label--float-above"]}
            id={"#{@id}-label"}
          >
            {@label}
          </span>
        <% else %>
          <span class="mdc-notched-outline">
            <span class="mdc-notched-outline__leading"></span>
            <span class="mdc-notched-outline__notch !w-fit">
              <span class="mdc-floating-label" id={"#{@id}-label"}>
                {@label}
              </span>
            </span>
            <span class="mdc-notched-outline__trailing"></span>
          </span>
        <% end %>
        <input
          type="text"
          class="mdc-text-field__input"
          id={@id}
          name={if @provide_hidden, do: @name <> "_", else: @name}
          value={Phoenix.HTML.Form.normalize_value("text", @value)}
          aria-labelledby={"#{@id}-label"}
          autocomplete="new-password"
          {@rest}
        />
        <span :if={@style == :filled} class="mdc-line-ripple"></span>
        <input
          :if={@provide_hidden}
          name={@name}
          value={Phoenix.HTML.Form.normalize_value("text", @value)}
        />
      </label>
      <div class="mdc-text-field-helper-line">
        <div :if={@helper} class="mdc-text-field-helper-text" aria-hidden="true">{@helper}</div>

        <div class="mdc-text-field-helper-text--validation-msg text-sm text-red-500 hidden jserror">
        </div>

        <div
          :for={msg <- @errors}
          class="mdc-text-field-helper-text--validation-msg text-sm text-red-500"
          aria-hidden="true"
        >
          {msg}
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Material Select
  """
  attr :id, :string, required: true
  attr :name, :string
  attr :value, :string
  attr :label, :string, default: nil
  attr :selected_text, :string, default: nil
  attr :required, :boolean, default: false
  attr :disabled, :boolean, default: false
  attr :style, :atom, values: [:filled, :outlined], default: :filled
  attr :custom_css, :string, default: ""
  attr :fixed, :boolean, default: false

  attr :field, Phoenix.HTML.FormField,
    doc: "a form field struct retrieved from the form, for example: @form[:email]"

  slot :option do
    attr :value, :string, required: true
    attr :label, :string, required: true
  end

  def material_select(%{field: %Phoenix.HTML.FormField{} = field} = assigns) do
    assigns
    |> assign(field: nil, id: assigns.id || field.id)
    |> assign(:add_hidden, true)
    |> assign(:errors, Enum.map(field.errors, &LiteCrmWeb.CoreComponents.translate_error(&1)))
    |> assign_new(:name, fn -> field.name end)
    |> assign_new(:value, fn -> field.value end)
    |> material_select()
  end

  def material_select(assigns) do
    ~H"""
    <div
      class={[
        "mdc-select",
        @style == :outlined && "mdc-select--outlined",
        @style == :filled && "mdc-select--filled",
        !@label && "mdc-select--no-label",
        @disabled && "mdc-select--disabled",
        @required && "mdc-select--required",
        @custom_css
      ]}
      id={@id}
      phx-hook="MaterialSelect"
    >
      <input
        :if={Map.has_key?(assigns, :add_hidden)}
        type="hidden"
        name={@name}
        value={Phoenix.HTML.Form.normalize_value("text", @value)}
      />
      <div
        class="mdc-select__anchor"
        role="button"
        aria-haspopup="listbox"
        aria-expanded="false"
        aria-labelledby={"#{@id}-label #{@id}-selected-text"}
        aria-disabled={if @disabled, do: "true", else: "false"}
        aria-required={if @required, do: "true", else: "false"}
      >
        <%= if @style == :filled do %>
          <span class="mdc-select__ripple"></span>
          <span :if={@label} id={"#{@id}-label"} class="mdc-floating-label">{@label}</span>
        <% else %>
          <span class="mdc-notched-outline">
            <span class="mdc-notched-outline__leading"></span>
            <span class="mdc-notched-outline__notch">
              <span :if={@label} id="outlined-select-label" class="mdc-floating-label">
                {@label}
              </span>
            </span>
            <span class="mdc-notched-outline__trailing"></span>
          </span>
        <% end %>
        <span class="mdc-select__selected-text-container">
          <span id={"#{@id}-selected-text"} class="mdc-select__selected-text">
            {@selected_text || @value}
          </span>
        </span>
        <span class="mdc-select__dropdown-icon">
          <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5" focusable="false">
            <polygon
              class="mdc-select__dropdown-icon-inactive"
              stroke="none"
              fill-rule="evenodd"
              points="7 10 12 15 17 10"
            >
            </polygon>
            <polygon
              class="mdc-select__dropdown-icon-active"
              stroke="none"
              fill-rule="evenodd"
              points="7 15 12 10 17 15"
            >
            </polygon>
          </svg>
        </span>
        <span :if={@style == :filled} class="mdc-line-ripple"></span>
      </div>

      <div
        class={[
          "mdc-select__menu mdc-menu mdc-menu-surface !z-100",
          @fixed && "mdc-menu-surface--fixed",
          !@fixed && "mdc-menu-surface--fullwidth"
        ]}
        id={"#{@id}-surface"}
      >
        <ul class="mdc-deprecated-list" role="listbox" aria-label={"#{@label} listbox"}>
          <%= for option <- @option do %>
            <li
              class={[
                "mdc-deprecated-list-item",
                @value == option.value && "mdc-deprecated-list-item--selected"
              ]}
              aria-selected={if @value == option.value, do: "true", else: "false"}
              data-value={option.value}
              role="option"
            >
              <span class="mdc-deprecated-list-item__ripple"></span>
              <span class="mdc-deprecated-list-item__text">
                {option.label}
              </span>
            </li>
          <% end %>
        </ul>
      </div>
    </div>
    """
  end

  attr :id, :string, required: true
  attr :name, :string, required: true
  attr :label, :string, default: nil
  attr :checked_value, :string, default: "true"
  attr :unchecked_value, :string, default: "false"
  attr :disabled, :boolean, default: false
  attr :custom_css, :string, default: nil

  def material_checkbox(assigns) do
    assigns =
      assign_new(assigns, :checked, fn ->
        Phoenix.HTML.Form.normalize_value(
          "checkbox",
          assigns[:value] == assigns[:checked_value] && assigns[:value]
        )
      end)

    ~H"""
    <div class={["mdc-checkbox", @disabled && "mdc-checkbox--disabled", @custom_css]}>
      <input type="hidden" name={@name} value={@unchecked_value} />
      <input
        type="checkbox"
        id={@id}
        name={@name}
        value="true"
        checked={@checked}
        class="mdc-checkbox__native-control"
      />
      <div class="mdc-checkbox__background">
        <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
          <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59" />
        </svg>
        <div class="mdc-checkbox__mixedmark"></div>
      </div>
      <div class="mdc-checkbox__ripple"></div>
      <div class="mdc-checkbox__focus-ring"></div>
    </div>
    <label :if={@label} for={@id}>
      {@label}
    </label>
    """
  end

  @doc """
  Material Data Table
  """
  attr :name, :string, required: true
  attr :id, :string, required: true

  slot :header do
    attr :label, :string, required: true
    attr :sort_value, :string, required: true
    attr :column_id, :string, required: true
  end

  slot :table_body

  attr :collection, :map, default: %{
    page: %{offset: 0, limit: 10, count: 0},
    count: 0,
    more?: false
  }
  attr :per_page, :string, default: "20"
  attr :page, :string, default: "1"
  attr :per_page_form, Phoenix.HTML.Form, default: nil
  attr :route, :string, default: ""
  attr :all_selected, :boolean, default: false
  attr :some_selected, :boolean, default: false

  def material_data_table(assigns) do
    assigns = assign_new(assigns, :per_page_form, fn -> to_form(%{"per_page" => assigns.per_page}) end)
    ~H"""
    <div
      class="mdc-data-table w-full"
      phx-hook="MaterialDataTable"
      id={@id}
      data-all-selected={@all_selected}
      data-some-selected={@some_selected}
    >
      <div class="mdc-data-table__table-container">
        <table class="mdc-data-table__table w-[calc(100vw - 16rem)]" aria-label={@name}>
          <thead>
            <tr class="mdc-data-table__header-row">
              <th
                class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox"
                role="columnheader"
                scope="col"
              >
                <div class={[
                  "mdc-checkbox mdc-data-table__header-row-checkbox",
                  @all_selected && "mdc-checkbox--selected"
                ]}>
                  <input
                    type="checkbox"
                    class="mdc-checkbox__native-control"
                    aria-label="Toggle all rows"
                    checked={@all_selected}
                  />
                  <div class="mdc-checkbox__background">
                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                      <path
                        class="mdc-checkbox__checkmark-path"
                        fill="none"
                        d="M1.73,12.91 8.1,19.28 22.79,4.59"
                      />
                    </svg>
                    <div class="mdc-checkbox__mixedmark"></div>
                  </div>
                  <div class="mdc-checkbox__ripple"></div>
                </div>
              </th>
              <%= for header <- @header do %>
                <th
                  class={[
                    "mdc-data-table__header-cell mdc-data-table__header-cell--with-sort",
                    header.sort_value != "none" && "mdc-data-table__header-cell--sorted"
                  ]}
                  role="columnheader"
                  scope="col"
                  aria-sort={header.sort_value}
                  data-column-id={header.column_id}
                >
                  <div class="mdc-data-table__header-cell-wrapper">
                    <div class="mdc-data-table__header-cell-label">{header.label}</div>
                    <button
                      class="mdc-icon-button material-icons mdc-data-table__sort-icon-button"
                      aria-label={"Sort by #{header.label}"}
                      aria-describedby={"#{header.column_id}-label"}
                    >
                      {if header.sort_value == "descending",
                        do: "arrow_downward",
                        else: "arrow_upward"}
                    </button>
                    <div
                      class="mdc-data-table__sort-status-label"
                      aria-hidden="true"
                      id={"#{header.column_id}-label"}
                    >
                    </div>
                  </div>
                </th>
              <% end %>
            </tr>
          </thead>
          <tbody class="mdc-data-table__content">
            {render_slot(@table_body)}
          </tbody>
        </table>
      </div>
      <div class="mdc-data-table__pagination">
        <div class="mdc-data-table__pagination-trailing">
          <div class="mdc-data-table__pagination-rows-per-page">
            <div class="mdc-data-table__pagination-rows-per-page-label">
              Rows per page
            </div>

            <.form :let={f} for={@per_page_form} phx-hook="PerPageForm" id="per-page-form">
              <.material_select
                field={f[:per_page]}
                id="per-page"
                style={:outlined}
                custom_css="mdc-data-table__pagination-rows-per-page-select mdc-data-table__pagination-rows-per-page-select--outlined"
              >
                <:option value="10" label="10" />
                <:option value="20" label="20" />
                <:option value="100" label="100" />
              </.material_select>
            </.form>

            <div class="mdc-data-table__pagination-navigation">
              <div class="mdc-data-table__pagination-total">
                {get_in(@collection, [:page, :offset]) || 0 + 1}‑{(get_in(@collection, [:page, :offset]) || 0) + (get_in(@collection, [:page, :limit]) || 10)} of {Map.get(@collection, :count, 0)}
              </div>
              <button
                class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                data-first-page="true"
                disabled={(get_in(@collection, [:page, :offset]) || 0) == 0}
                phx-value-page="1"
                phx-click="paginate"
              >
                <div class="mdc-button__icon">first_page</div>
              </button>
              <button
                class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                data-prev-page="true"
                disabled={(get_in(@collection, [:page, :offset]) || 0) == 0}
                phx-value-page={max(String.to_integer(@page) - 1, 1)}
                phx-click="paginate"
              >
                <div class="mdc-button__icon">chevron_left</div>
              </button>
              <button
                class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                data-next-page="true"
                disabled={!Map.get(@collection, :more?, false)}
                phx-value-page={String.to_integer(@page) + 1}
                phx-click="paginate"
              >
                <div class="mdc-button__icon">chevron_right</div>
              </button>
              <button
                class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                data-last-page="true"
                disabled={!Map.get(@collection, :more?, false)}
                phx-value-page={ceil(Map.get(@collection, :count, 0) / String.to_integer(@per_page))}
                phx-click="paginate"
              >
                <div class="mdc-button__icon">last_page</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @doc """
  Renders flash notices.

  ## Examples

      <.flash kind={:info} flash={@flash} />
      <.flash kind={:info} phx-mounted={show("#flash")}>Welcome Back!</.flash>
  """
  attr :id, :string, doc: "the optional id of flash container"
  attr :flash, :map, default: %{}, doc: "the map of flash messages to display"
  attr :title, :string, default: nil
  attr :kind, :atom, values: [:info, :error], doc: "used for styling and flash lookup"
  attr :rest, :global, doc: "the arbitrary HTML attributes to add to the flash container"

  slot :inner_block, doc: "the optional inner block that renders the flash message"

  def flash(assigns) do
    assigns = assign_new(assigns, :id, fn -> "flash-#{assigns.kind}" end)

    ~H"""
    <div
      :if={msg = render_slot(@inner_block) || Phoenix.Flash.get(@flash, @kind)}
      id={@id}
      phx-click={JS.push("lv:clear-flash", value: %{key: @kind}) |> hide("##{@id}")}
      role="status"
      aria-relevant="additions"
      class={[
        "mdc-snackbar__surface m-2",
        @kind == :info && "",
        @kind == :error && ""
      ]}
      {@rest}
    >
      <%!-- <p :if={@title} class="flex items-center gap-1.5 text-sm font-semibold leading-6">
        <.icon :if={@kind == :info} name="hero-information-circle-mini" class="h-4 w-4" />
        <.icon :if={@kind == :error} name="hero-exclamation-circle-mini" class="h-4 w-4" />
        <%= @title %>
      </p> --%>
      <div class="mdc-snackbar__label" aria-atomic="false">{msg}</div>
      <%!-- <button type="button" class="group absolute top-1 right-1 p-2" aria-label={gettext("close")}>
        <.icon name="hero-x-mark-solid" class="h-5 w-5 opacity-40 group-hover:opacity-70" />
      </button> --%>
      <!--          <div class="mdc-snackbar__actions" aria-atomic="true">-->
                <!--            <button type="button" class="mdc-button mdc-snackbar__action">-->
                <!--              <div class="mdc-button__ripple"></div>-->
                <!--              <span class="mdc-button__label">Retry</span>-->
                <!--            </button>-->
                <!--          </div>-->
    </div>
    """
  end

  @doc """
  Shows the flash group with standard titles and content.

  ## Examples

      <.flash_group flash={@flash} />
  """
  attr :flash, :map, required: true, doc: "the map of flash messages"
  attr :id, :string, default: "flash-group", doc: "the optional id of flash container"

  def flash_group(assigns) do
    ~H"""
    <aside class="mdc-snackbar flex flex-col" phx-hook="MaterialSnackbar" id={@id}>
      <.flash kind={:info} title={gettext("Success!")} flash={@flash} />
      <.flash kind={:error} title={gettext("Error!")} flash={@flash} />
      <.flash
        id="client-error"
        kind={:error}
        title={gettext("We can't find the internet")}
        phx-disconnected={show(".phx-client-error #client-error")}
        phx-connected={hide("#client-error")}
        hidden
      >
        {gettext("Attempting to reconnect")}
        <CoreComponents.icon name="hero-arrow-path" class="ml-1 h-3 w-3 animate-spin" />
      </.flash>

      <.flash
        id="server-error"
        kind={:error}
        title={gettext("Something went wrong!")}
        phx-disconnected={show(".phx-server-error #server-error")}
        phx-connected={hide("#server-error")}
        hidden
      >
        {gettext("Hang in there while we get back on track")}
        <CoreComponents.icon name="hero-arrow-path" class="ml-1 h-3 w-3 animate-spin" />
      </.flash>
    </aside>
    """
  end

  ## JS Commands

  def show(js \\ %JS{}, selector) do
    JS.show(js,
      to: selector,
      transition:
        {"transition-all transform ease-out duration-300",
         "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95",
         "opacity-100 translate-y-0 sm:scale-100"}
    )
  end

  def hide(js \\ %JS{}, selector) do
    JS.hide(js,
      to: selector,
      time: 200,
      transition:
        {"transition-all transform ease-in duration-200",
         "opacity-100 translate-y-0 sm:scale-100",
         "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"}
    )
  end

  def show_modal(js \\ %JS{}, id) do
    js
    |> JS.dispatch("open-modal", to: "##{id}", detail: %{id: id})
  end

  def hide_modal(js \\ %JS{}, id) do
    js
    |> JS.dispatch("close-modal", to: "##{id}", detail: %{id: id})
  end

  def aria_sort(assigns, key, default \\ false) do
    order = Map.get(assigns, :order)
    reverse = Map.get(assigns, :reverse)

    if order == key || (order == nil && default) do
      if reverse == true || (reverse == nil && default) do
        "descending"
      else
        "ascending"
      end
    else
      "none"
    end
  end
end
