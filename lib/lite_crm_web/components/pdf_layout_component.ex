defmodule LiteCrmWeb.PdfLayoutComponent do
  @moduledoc """
  PDF Layout for all PDFs.
  """
  use Phoenix.Component

  def render(assigns) do
    ~H"""
    <html xmlns="http://www.w3.org/1999/xhtml">
      <head>
        <meta content="text/html; charset=UTF-8" http-equiv="Content-Type" />
        <meta content="width=device-width" name="viewport" />
        <style type="text/css">
          <%= @style %>
          .page-break {
              display: block;
              clear: both;
              page-break-after: always;
          }
        </style>
      </head>
      <body>
        {@inner_content}
      </body>
    </html>
    """
  end
end
