<!DOCTYPE html>
<html lang="en" class="[scrollbar-gutter:stable]">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <.live_title default="LiteCrm" suffix=" · Phoenix Framework">
      {assigns[:page_title]}
    </.live_title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}>
    </script>
    <%= if Application.get_env(:live_debugger, :browser_features?) do %>
      <script id="live-debugger-scripts" src={Application.get_env(:live_debugger, :assets_url)}>
      </script>
    <% end %>
  </head>
  <body class="bg-white lite-crm-web">
    {@inner_content}
  </body>
</html>
