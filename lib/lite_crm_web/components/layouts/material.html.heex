<MaterialComponents.flash_group flash={@flash} />

<MaterialComponents.aside id="main" disabled={@drawer_disabled}>
  <:header><a href={~p"/"} class="text-white font-extrabold tracking-tight">LiteCRM</a></:header>
  <:sidebar_menu>
    <MaterialComponents.sidebar_menu>
      <:link href={~p"/"} current={@current_url == "/"} icon="dashboard" id="dashboard">
        Dashboard
      </:link>
      <:link
        href={~p"/leads"}
        current={String.starts_with?(@current_url, "/leads")}
        icon="move_to_inbox"
        id="leads"
      >
        Leads
      </:link>
      <:link
        href={~p"/users"}
        current={String.starts_with?(@current_url, "/users")}
        icon="group"
        id="users"
      >
        Users
      </:link>
    </MaterialComponents.sidebar_menu>
  </:sidebar_menu>
</MaterialComponents.aside>

<div class="mdc-drawer-app-content h-screen">
  <%!-- <%= render partial: 'common/banner' %> --%>
  <MaterialComponents.top_app_bar
    id="app-bar"
    drawer_disabled={@drawer_disabled}
    current_user={@current_user}
    current_admin_user={Map.get(assigns, :current_admin_user)}
  >
    <:heading>
      <%= if function_exported?(@socket.view, :heading_section, 1) do %>
        {@socket.view.heading_section(assigns)}
      <% end %>
    </:heading>
  </MaterialComponents.top_app_bar>
  <main
    class="main-content mdc-top-app-bar--fixed-adjust overflow-y-auto scroll-smooth min-h-full"
    id="main-content"
  >
    {@inner_content}
  </main>
</div>
