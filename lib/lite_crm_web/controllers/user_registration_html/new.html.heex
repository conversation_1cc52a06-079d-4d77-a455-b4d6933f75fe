<div class="mx-auto max-w-sm">
  <.header class="text-center">
    Register for an account
    <:subtitle>
      Already registered?
      <.link navigate={~p"/sign-in"} class="font-semibold text-brand hover:underline">
        Sign in
      </.link>
      to your account now.
    </:subtitle>
  </.header>

  <.simple_form :let={f} for={@changeset} action={~p"/sign-up"}>
    <.error :if={@changeset.action}>
      Oops, something went wrong! Please check the errors below.
    </.error>

    <.input field={f[:email]} type="email" label="Email" required />
    <.input field={f[:first_name]} type="text" label="First Name" required />
    <.input field={f[:last_name]} type="text" label="Last Name" required />
    <.input field={f[:password]} type="password" label="Password" required />

    <:actions>
      <.button phx-disable-with="Creating account..." class="w-full">Create an account</.button>
    </:actions>
  </.simple_form>
</div>
