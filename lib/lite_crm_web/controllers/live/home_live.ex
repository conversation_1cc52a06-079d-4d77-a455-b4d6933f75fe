defmodule LiteCrmWeb.HomeLive do
  use LiteCrmWeb, :live_view

  @impl true
  def render(assigns) do
    ~H"""
    <main class="isolate">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 pb-16 pt-20 text-center lg:pt-32">
        <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">LiteCRM</h1>
        Simple starter Phoenix LiveView CRM like application with Material Design interface.
      </div>
    </main>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    socket = assign(socket, :drawer_disabled, true)

    {:ok, socket, layout: {LiteCrmWeb.Layouts, :material}}
  end
end
