defmodule LiteCrmWeb.LeadsLive do
  alias LiteCrmWeb.MaterialComponents
  use LiteCrmWeb, :live_view
  alias LiteCrm.Crm
  alias LiteCrm.Crm.Lead

  @impl true
  def render(assigns) do
    ~H"""
    <div class="flex flex-col pt-36">
      <MaterialComponents.material_data_table
        name="Leads"
        id="lead-table"
        collection={@leads}
        per_page={@per_page}
        page={@page}
        per_page_form={@per_page_form}
        route="/leads"
        all_selected={if @all_selected, do: "true", else: "false"}
        some_selected={if @some_selected, do: "true", else: "false"}
      >
        <:header label="ID" sort_value={aria_sort(assigns, "id")} column_id="id" />
        <:header
          label="Description"
          sort_value={aria_sort(assigns, "description")}
          column_id="description"
        />
        <:header
          label="Last Updated"
          sort_value={aria_sort(assigns, "updated_at", true)}
          column_id="updated_at"
        />
        <:table_body>
          <%= for lead <- @leads.results do %>
            <tr data-row-id={lead.id} class="mdc-data-table__row">
              <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                <div class={[
                  "mdc-checkbox mdc-data-table__row-checkbox",
                  lead_selected(@current_user.id, lead.id) && "mdc-checkbox--selected"
                ]}>
                  <input
                    type="checkbox"
                    class="mdc-checkbox__native-control"
                    aria-labelledby={lead.id}
                    checked={lead_selected(@current_user.id, lead.id)}
                  />
                  <div class="mdc-checkbox__background">
                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                      <path
                        class="mdc-checkbox__checkmark-path"
                        fill="none"
                        d="M1.73,12.91 8.1,19.28 22.79,4.59"
                      />
                    </svg>
                    <div class="mdc-checkbox__mixedmark"></div>
                  </div>
                  <div class="mdc-checkbox__ripple"></div>
                </div>
              </td>
              <th class="mdc-data-table__cell" scope="row">{lead.id}</th>
              <td class="mdc-data-table__cell">
                <a href="" class="mdc-button">{lead.description}</a>
              </td>
              <td class="mdc-data-table__cell mdl-data-table__cell--non-numeric">
                <time datetime={lead.updated_at}>
                  {LiteCrm.Cldr.format_time(lead.updated_at, format: :medium)}
                </time>
              </td>
            </tr>
          <% end %>
        </:table_body>
      </MaterialComponents.material_data_table>
    </div>
    """
  end

  def heading_section(assigns) do
    ~H"""
    <section class="w-full">
      <div class="flex flex-col text-white bg-secondary p-4 h-24">
        <div class="flex justify-end"></div>
      </div>
    </section>
    <.form :let={f} for={@search_form} phx-change="search">
      <MaterialComponents.material_input
        field={f[:filter]}
        label="Search by Name, Email, or ID"
        id="filter"
      />
    </.form>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    socket =
      assign(socket,
        create_form: to_form(Crm.change_lead(%Lead{})),
        search_form: to_form(%{"filter" => nil}),
        drawer_disabled: false,
        all_selected: false,
        filter: nil
      )

    {:ok, socket, layout: {LiteCrmWeb.Layouts, :material}}
  end

  @impl true
  def handle_params(params, _, socket) do
    per_page = String.to_integer(params["per_page"] || "10")
    page = String.to_integer(params["page"] || "1")
    order = params["order"] || "updated_at"
    reverse = (params["reverse"] || "true") == "true"
    filter = params["filter"]
    direction = if reverse == true, do: :desc, else: :asc

    leads =
      Crm.list_leads(
        filter: filter,
        order: String.to_atom(order),
        direction: direction,
        page: page,
        per_page: per_page
      )

    count = Crm.count_leads(filter)

    # Create a struct similar to what Ash.Query returns for compatibility
    leads_data = %{
      results: leads,
      count: count,
      more?: (page - 1) * per_page + length(leads) < count,
      page: %{
        limit: per_page,
        offset: (page - 1) * per_page,
        count: count
      }
    }

    selected_ids = get_selected_ids(socket.assigns.current_user.id)
    all_selected = get_all_ids() -- selected_ids == []

    socket =
      assign(socket,
        leads: leads_data,
        lead_selector: lead_selector(leads),
        order: order,
        reverse: reverse,
        filter: filter,
        search_form: to_form(%{"filter" => filter}),
        per_page_form: to_form(%{"per_page" => Integer.to_string(per_page)}),
        page: Integer.to_string(page),
        per_page: Integer.to_string(per_page),
        update_form: to_form(Crm.change_lead(List.first(leads, %Lead{}))),
        all_selected: all_selected,
        some_selected: !all_selected && selected_ids != []
      )

    {:noreply, socket}
  end

  def handle_event("delete_lead", %{"lead-id" => lead_id}, socket) do
    lead = Crm.get_lead!(lead_id)
    {:ok, _} = Crm.delete_lead(lead)

    # Redirect to refresh the page with updated data
    {:noreply, push_patch(socket, to: build_query_string(socket, %{}))}
  end

  def handle_event("create_lead", %{"form" => %{"description" => description}}, socket) do
    {:ok, _lead} = Crm.create_lead(%{description: description})

    # Redirect to refresh the page with updated data
    {:noreply, push_patch(socket, to: build_query_string(socket, %{}))}
  end

  def handle_event("update_lead", %{"form" => form_params}, socket) do
    %{"lead_id" => lead_id, "description" => description} = form_params

    lead = Crm.get_lead!(lead_id)
    {:ok, _} = Crm.update_lead(lead, %{description: description})

    # Redirect to refresh the page with updated data
    {:noreply, push_patch(socket, to: build_query_string(socket, %{}))}
  end

  @impl true
  def handle_event("toggle-drawer", _, socket) do
    {:noreply, push_event(socket, "toggle-drawer", %{})}
  end

  @impl true
  def handle_event(
        "material-data-table-sorted",
        %{"order" => order, "reverse" => reverse},
        socket
      ) do
    {:noreply,
     push_patch(socket, to: build_query_string(socket, %{order: order, reverse: reverse}))}
  end

  @impl true
  def handle_event(
        "paginate",
        %{"page" => page},
        socket
      ) do
    {:noreply, push_patch(socket, to: build_query_string(socket, %{page: page}))}
  end

  @impl true
  def handle_event(
        "per_page_changed",
        %{"per_page" => per_page},
        socket
      ) do
    {:noreply, push_patch(socket, to: build_query_string(socket, %{per_page: per_page}))}
  end

  @impl true
  def handle_event(
        "material-data-table-row-selected",
        %{"row_id" => row_id, "selected" => selected},
        socket
      ) do
    toggle_selection(socket.assigns.current_user.id, row_id, selected)
    {:noreply, socket}
  end

  @impl true
  def handle_event(
        "material-data-table-all-selected",
        %{},
        socket
      ) do
    toggle_all_selected(socket.assigns.current_user.id, true)
    {:noreply, socket}
  end

  @impl true
  def handle_event(
        "material-data-table-all-unselected",
        %{},
        socket
      ) do
    toggle_all_selected(socket.assigns.current_user.id, false)
    {:noreply, socket}
  end

  @impl true
  def handle_event("search", %{"filter" => query}, socket) do
    {:noreply, push_patch(socket, to: build_query_string(socket, %{"filter" => query}))}
  end

  defp lead_selector(leads) do
    for lead <- leads do
      {lead.description, lead.id}
    end
  end

  defp selected_leads_index(user_id), do: String.to_atom("#{user_id}_selected_leads")

  defp get_selected_ids(user_id) do
    GenServer.call(
      LiteCrm.UiSettings,
      {:values, selected_leads_index(user_id)}
    )
  end

  defp lead_selected(user_id, id) do
    Enum.member?(
      get_selected_ids(user_id),
      id
    )
  end

  defp toggle_selection(user_id, id, selected) do
    GenServer.cast(
      LiteCrm.UiSettings,
      {:toggle, selected_leads_index(user_id), String.to_integer(id), selected}
    )
  end

  defp get_all_ids() do
    Crm.list_leads(per_page: 1000)
    |> Enum.map(fn lead -> lead.id end)
  end

  defp toggle_all_selected(user_id, selected) do
    GenServer.cast(
      LiteCrm.UiSettings,
      {:toggle, selected_leads_index(user_id), get_all_ids(), selected}
    )
  end

  # These functions are no longer needed as filtering is handled in the context
  # defp maybe_search_leads(query, search) when byte_size(search) > 0 do
  #   # Handled in the context
  # end
  #
  # defp maybe_search_leads(query, _search), do: query

  defp build_query_string(socket, update) do
    query =
      Map.filter(
        %{
          order: socket.assigns.order,
          reverse: socket.assigns.reverse,
          filter: socket.assigns.filter,
          page: socket.assigns.page,
          per_page: socket.assigns.per_page
        },
        fn {_, v} -> v != nil end
      )
      |> Map.merge(update)

    "/leads?" <> URI.encode_query(query)
  end
end
