defmodule LiteCrmWeb.UsersLive do
  alias LiteCrmWeb.MaterialComponents
  use LiteCrmWeb, :live_view
  alias LiteCrm.Accounts
  alias LiteCrm.Accounts.User

  @impl true
  def render(assigns) do
    ~H"""
    <div class="flex flex-col pt-36">
      <MaterialComponents.material_data_table
        name="Users"
        id="users-table"
        collection={@users}
        per_page={@per_page}
        page={@page}
        per_page_form={@per_page_form}
        route="/users"
        all_selected={if @all_selected, do: "true", else: "false"}
        some_selected={if @some_selected, do: "true", else: "false"}
      >
        <:header label="ID" sort_value={aria_sort(assigns, "id")} column_id="id" />
        <:header label="Email" sort_value={aria_sort(assigns, "email")} column_id="email" />
        <:header
          label="Last Updated"
          sort_value={aria_sort(assigns, "updated_at", true)}
          column_id="updated_at"
        />
        <:table_body>
          <%= for user <- @users.results do %>
            <tr data-row-id={user.id} class="mdc-data-table__row">
              <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                <div class={[
                  "mdc-checkbox mdc-data-table__row-checkbox",
                  user_selected(@current_user.id, user.id) && "mdc-checkbox--selected"
                ]}>
                  <input
                    type="checkbox"
                    class="mdc-checkbox__native-control"
                    aria-labelledby={user.id}
                    checked={user_selected(@current_user.id, user.id)}
                  />
                  <div class="mdc-checkbox__background">
                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                      <path
                        class="mdc-checkbox__checkmark-path"
                        fill="none"
                        d="M1.73,12.91 8.1,19.28 22.79,4.59"
                      />
                    </svg>
                    <div class="mdc-checkbox__mixedmark"></div>
                  </div>
                  <div class="mdc-checkbox__ripple"></div>
                </div>
              </td>
              <th class="mdc-data-table__cell" scope="row">{user.id}</th>
              <td class="mdc-data-table__cell">
                <.link patch={~p"/users/#{user.id}"} class="mdc-button">{user.email}</.link>
              </td>
              <td class="mdc-data-table__cell mdl-data-table__cell--non-numeric">
                <time datetime={user.updated_at}>
                  {LiteCrm.Cldr.format_time(user.updated_at, format: :medium)}
                </time>
              </td>
            </tr>
          <% end %>
        </:table_body>
      </MaterialComponents.material_data_table>
    </div>
    <MaterialComponents.form_modal
      id="new_user_modal"
      title="Create User"
      additional_classes="!min-w-[36rem] !min-h-[14rem]"
      form={@create_form}
      ok_button="Create User"
      submit="create_user"
      change="validate"
      opened={@new_user_dialog_opened}
    >
      <:content>
        <div class="flex flex-col lg:flex-row px-4 py-2 gap-2">
          <div class="w-full lg:w-1/2">
            <MaterialComponents.material_input
              field={@create_form[:first_name]}
              label="First Name"
              id="user-first_name"
              required
            />
          </div>
          <div class="w-full lg:w-1/2">
            <MaterialComponents.material_input
              field={@create_form[:last_name]}
              label="Last Name"
              id="user-last_name"
              required
            />
          </div>
        </div>
        <div class="flex flex-col lg:flex-row px-4 py-2 gap-2">
          <div class="w-full">
            <MaterialComponents.material_input
              field={@create_form[:email]}
              label="Email"
              id="user-email"
              required
            />
          </div>
        </div>
      </:content>
    </MaterialComponents.form_modal>
    """
  end

  def heading_section(assigns) do
    ~H"""
    <section class="w-full">
      <div class="flex flex-col text-white bg-secondary p-4 h-24">
        <div class="flex justify-end">
          <div class="mdc-touch-target-wrapper">
            <button
              class="mdc-fab mdc-fab--mini mdc-fab--touch !bg-red-500"
              aria-label="Add User"
              phx-click="new_user_modal"
            >
              <div class="mdc-fab__ripple"></div>
              <div class="mdc-fab__focus-ring"></div>
              <span class="material-icons mdc-fab__icon">add</span>
              <div class="mdc-fab__touch"></div>
            </button>
          </div>
        </div>
      </div>
    </section>
    <.form :let={f} for={@search_form} phx-change="search">
      <MaterialComponents.material_input
        field={f[:filter]}
        label="Search by Name, Email, or ID"
        id="filter"
      />
    </.form>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    socket =
      assign(socket,
        create_form: to_form(Accounts.change_user_registration(%User{})),
        search_form: to_form(%{"filter" => nil}),
        drawer_disabled: false,
        all_selected: false,
        filter: nil,
        new_user_dialog_opened: false
      )

    {:ok, socket, layout: {LiteCrmWeb.Layouts, :material}}
  end

  @impl true
  def handle_params(params, _, socket) do
    per_page = String.to_integer(params["per_page"] || "10")
    page = String.to_integer(params["page"] || "1")
    order = params["order"] || "updated_at"
    reverse = (params["reverse"] || "true") == "true"
    filter = params["filter"]
    direction = if reverse == true, do: :desc, else: :asc

    users =
      Accounts.list_users(
        filter: filter,
        order: String.to_atom(order),
        direction: direction,
        page: page,
        per_page: per_page
      )

    count = Accounts.count_users(filter)

    # Create a struct similar to what Ash.Query returns for compatibility
    users_data = %{
      results: users,
      count: count,
      more?: (page - 1) * per_page + length(users) < count,
      page: %{
        limit: per_page,
        offset: (page - 1) * per_page,
        count: count
      }
    }

    selected_ids = get_selected_ids(socket.assigns.current_user.id)
    all_selected = get_all_ids() -- selected_ids == []

    socket =
      assign(socket,
        users: users_data,
        user_selector: user_selector(users),
        order: order,
        reverse: reverse,
        filter: filter,
        direction: direction,
        search_form: to_form(%{"filter" => filter}),
        per_page_form: to_form(%{"per_page" => Integer.to_string(per_page)}),
        page: Integer.to_string(page),
        per_page: Integer.to_string(per_page),
        update_form: to_form(Accounts.change_user_registration(List.first(users, %User{}))),
        all_selected: all_selected,
        some_selected: !all_selected && selected_ids != []
      )

    {:noreply, socket}
  end

  def handle_event("validate", %{"form" => params}, socket) do
    changeset =
      %User{}
      |> User.registration_changeset(params, hash_password: false)
      |> Map.put(:action, :validate)

    {:noreply,
     assign(socket,
       create_form: to_form(changeset),
       new_user_dialog_opened: true
     )}
  end

  def handle_event(
        "create_user",
        %{"form" => params},
        socket
      ) do
    # Generate a random password for the user
    password = random_password()
    params = Map.put(params, "password", password)

    case Accounts.register_user(params) do
      {:ok, user} ->
        # Redirect to refresh the page with updated data
        socket =
          socket
          |> put_flash(:info, "Created User #{user.email}!")
          |> assign(
            create_form: to_form(%User{}),
            new_user_dialog_opened: false
          )
          |> push_patch(to: build_query_string(socket, %{}))

        {:noreply, socket}

      {:error, changeset} ->
        {:noreply, assign(socket, create_form: to_form(changeset), new_user_dialog_opened: true)}
    end
  end

  def handle_event("update_user", %{"form" => form_params}, socket) do
    %{"user_id" => user_id, "first_name" => first_name, "last_name" => last_name} = form_params

    user = Accounts.get_user!(user_id)
    {:ok, _} = Accounts.update_user(user, %{first_name: first_name, last_name: last_name})

    # Redirect to refresh the page with updated data
    {:noreply, push_patch(socket, to: build_query_string(socket, %{}))}
  end

  @impl true
  def handle_event("toggle-drawer", _, socket) do
    {:noreply, push_event(socket, "toggle-drawer", %{})}
  end

  @impl true
  def handle_event("new_user_modal", _, socket) do
    {:noreply, push_event(socket, "open-modal", %{id: "new_user_modal"})}
  end

  @impl true
  def handle_event("modal-closed", _, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event(
        "material-data-table-sorted",
        %{"order" => order, "reverse" => reverse},
        socket
      ) do
    {:noreply,
     push_patch(socket, to: build_query_string(socket, %{order: order, reverse: reverse}))}
  end

  @impl true
  def handle_event(
        "paginate",
        %{"page" => page},
        socket
      ) do
    {:noreply, push_patch(socket, to: build_query_string(socket, %{page: page}))}
  end

  @impl true
  def handle_event(
        "per_page_changed",
        %{"per_page" => per_page},
        socket
      ) do
    {:noreply, push_patch(socket, to: build_query_string(socket, %{per_page: per_page}))}
  end

  @impl true
  def handle_event(
        "material-data-table-row-selected",
        %{"row_id" => row_id, "selected" => selected},
        socket
      ) do
    toggle_selection(socket.assigns.current_user.id, row_id, selected)
    {:noreply, socket}
  end

  @impl true
  def handle_event(
        "material-data-table-all-selected",
        %{},
        socket
      ) do
    toggle_all_selected(socket.assigns.current_user.id, true)
    {:noreply, socket}
  end

  @impl true
  def handle_event(
        "material-data-table-all-unselected",
        %{},
        socket
      ) do
    toggle_all_selected(socket.assigns.current_user.id, false)
    {:noreply, socket}
  end

  @impl true
  def handle_event("search", %{"filter" => query}, socket) do
    {:noreply, push_patch(socket, to: build_query_string(socket, %{"filter" => query}))}
  end

  defp user_selector(users) do
    for user <- users do
      {user.email, user.id}
    end
  end

  defp selected_users_index(user_id), do: String.to_atom("#{user_id}_selected_users")

  defp get_selected_ids(user_id) do
    GenServer.call(
      LiteCrm.UiSettings,
      {:values, selected_users_index(user_id)}
    )
  end

  defp user_selected(user_id, id) do
    Enum.member?(
      get_selected_ids(user_id),
      id
    )
  end

  defp toggle_selection(user_id, id, selected) do
    GenServer.cast(
      LiteCrm.UiSettings,
      {:toggle, selected_users_index(user_id), String.to_integer(id), selected}
    )
  end

  defp get_all_ids() do
    Accounts.list_all_user_ids()
  end

  defp toggle_all_selected(user_id, selected) do
    GenServer.cast(
      LiteCrm.UiSettings,
      {:toggle, selected_users_index(user_id), get_all_ids(), selected}
    )
  end

  # These functions are no longer needed as filtering is handled in the context
  # defp maybe_search_users(query, search) when byte_size(search) > 0 do
  #   # Handled in the context
  # end
  #
  # defp maybe_search_users(query, _search), do: query

  defp build_query_string(socket, update) do
    query =
      Map.filter(
        %{
          order: socket.assigns.order,
          reverse: socket.assigns.reverse,
          filter: socket.assigns.filter,
          page: socket.assigns.page,
          per_page: socket.assigns.per_page
        },
        fn {_, v} -> v != nil end
      )
      |> Map.merge(update)

    "/users?" <> URI.encode_query(query)
  end

  defp random_password() do
    :crypto.strong_rand_bytes(16) |> Base.encode64() |> String.slice(0..15)
  end

  # This function is no longer needed as it's handled in handle_params
  # defp load_users(filter, order, direction, page, per_page, current_user_id) do
  #   # Handled in handle_params
  # end
end
