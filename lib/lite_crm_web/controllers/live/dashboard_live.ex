defmodule LiteCrmWeb.DashboardLive do
  use LiteCrmWeb, :live_view

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="mdc-drawer-app-content h-[calc(100vh-4rem)] overflow-y-auto overflow-x-hidden"
      id="main-scroll"
    >
      <section class="w-full">
        <div class="flex flex-col justify-end text-white bg-secondary h-36">
          <div class="flex justify-between">
            <h1 class="px-6 py-6 text-3xl text-bold"></h1>
          </div>
          <div class="mx-auto shadow-md clip-path-bottom border-b border-slate-200 bg-white text-black text-2xl px-6 py-2 w-5/6">
            Dashboard
          </div>
        </div>
      </section>
      <div class="mx-auto shadow-md bg-white text-black px-6 py-2 relative w-5/6">
        <div class="grid grid-cols-1 w-full">
          <!-- Add dashboard graphs here -->
        </div>
      </div>
      <div class="h-6"></div>
    </div>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    socket = assign(socket, :drawer_disabled, false)

    {:ok, socket, layout: {LiteCrmWeb.Layouts, :material}}
  end

  @impl true
  def handle_event("toggle-drawer", _, socket) do
    {:noreply, push_event(socket, "toggle-drawer", %{})}
  end
end
