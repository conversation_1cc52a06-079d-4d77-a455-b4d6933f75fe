defmodule LiteCrmWeb.UserLive do
  alias LiteCrmWeb.MaterialComponents
  use LiteCrmWeb, :live_view
  alias LiteCrm.Accounts
  alias LiteCrm.Accounts.User

  @impl true
  def render(assigns) do
    ~H"""
    """
  end

  def heading_section(assigns) do
    ~H"""
    <section class="w-full">
      <div class="flex text-white bg-secondary p-4 justify-between">
        <div class="flex flex-col">
          <h3 class="font-bold">{User.full_name(@user)}</h3>
          <div class="flex items-center">
            <span>{@user.email}</span>
            <.link :if={@current_user.admin} href={~p"/impersonate/#{@user.id}"}>
              <i class="material-icons pl-2">key</i>
            </.link>
            <.link href={~p"/users/#{@user.id}/pdf"}>
              <i class="material-icons pl-2">picture_as_pdf</i>
            </.link>
          </div>
        </div>
      </div>
      <MaterialComponents.tab_bar_menu>
        <:link href={~p"/users/#{@user.id}"} current={@current_url == "/users/#{@user.id}"}>
          Details
        </:link>
        <:link
          href={~p"/users/#{@user.id}/roles"}
          current={@current_url == "/users/#{@user.id}/roles"}
        >
          Roles
        </:link>
      </MaterialComponents.tab_bar_menu>
    </section>
    """
  end

  @impl true
  def mount(%{"id" => user_id}, _session, socket) do
    socket =
      assign(socket,
        drawer_disabled: false,
        user: Accounts.get_user!(user_id)
      )

    {:ok, socket, layout: {LiteCrmWeb.Layouts, :material}}
  end

  @impl true
  def handle_params(_params, _, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle-drawer", _, socket) do
    {:noreply, push_event(socket, "toggle-drawer", %{})}
  end
end
