defmodule LiteCrmWeb.UserSessionController do
  use LiteCrmWeb, :controller

  alias LiteCrm.Accounts
  alias LiteCrmWeb.UserAuth

  def new(conn, _params) do
    render(conn, :new, error_message: nil)
  end

  def create(conn, %{"user" => user_params}) do
    %{"email" => email, "password" => password} = user_params

    if user = Accounts.get_user_by_email_and_password(email, password) do
      UserAuth.log_in_user(conn, user, user_params)
    else
      # In order to prevent user enumeration attacks, don't disclose whether the email is registered.
      render(conn, :new, error_message: "Invalid email or password")
    end
  end

  def delete(conn, _params) do
    conn
    |> put_flash(:info, "Logged out successfully.")
    |> UserAuth.log_out_user()
  end

  def impersonate(conn, %{"user_id" => user_id}) do
    current_user = conn.assigns.current_user

    if current_user.id == String.to_integer(user_id) do
      conn
      |> put_flash(:info, "You are already logged in as #{current_user.email}")
      |> redirect(to: ~p"/")
    else
      # Check if current user is admin
      if current_user.admin do
        user = Accounts.get_user!(user_id)

        conn
        |> put_session(:admin_token, Accounts.generate_user_session_token(current_user))
        |> put_flash(
          :info,
          "You are now logged in as #{user.email}. You can resume your admin session using the menu."
        )
        |> UserAuth.log_in_user(user)
      else
        conn
        |> put_flash(:info, "Access Denied")
        |> redirect(to: ~p"/")
      end
    end
  end

  def resume(conn, _params) do
    return_to = get_session(conn, :user_return_to) || ~p"/"

    current_admin_user =
      get_session(conn, :admin_token)
      |> Accounts.get_user_by_session_token()

    if current_admin_user do
      # Clear the session first to remove the impersonated user
      conn =
        conn
        |> configure_session(renew: true)
        |> clear_session()

      # Then log in as the admin user
      conn
      |> put_flash(:info, "You are now logged in as yourself again")
      |> UserAuth.log_in_user(current_admin_user)
      |> redirect(to: return_to)
    else
      conn
      |> clear_session()
      |> redirect(to: return_to)
    end
  end
end
