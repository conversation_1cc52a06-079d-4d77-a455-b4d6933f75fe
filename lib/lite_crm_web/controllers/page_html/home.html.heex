<MaterialComponents.flash_group flash={@flash} />
<aside
  class="mdc-drawer mdc-drawer--dismissible !w-64 main-drawer !z-50 mdc-drawer--open "
  id="drawer"
  data-controller="main-drawer"
  data-main-drawer-material-topbar-outlet="#app-bar"
  data-main-drawer-opened-value="true"
  data-main-drawer-name-value="main"
  data-main-drawer-open-url-value=""
  data-main-drawer-close-url-value=""
>
  <div class="mdc-drawer__content bg-slate-800 fixed top-0 !w-64">
    <header class="flex flex-row items-center pl-6 h-16 min-h-16 bg-primary">
      LiteCRM
    </header>
    <%!-- <%= yield(:sidebar_menu) %> --%>
  </div>
</aside>
<nav class="bg-gray-800">
  <div class="px-2 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <div class="relative flex items-center justify-between h-16">
      <div class="flex items-center justify-center flex-1 sm:items-stretch sm:justify-start">
        <div class="block ml-6">
          <div class="flex space-x-4">
            <div class="px-3 py-2 text-xl font-medium text-white ">
              Ash Demo
            </div>
          </div>
        </div>
      </div>
      <div class="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0">
        <%= if @current_user do %>
          <span class="px-3 py-2 text-sm font-medium text-white rounded-md">
            {@current_user.email}
          </span>
          <a
            href="/sign-out"
            class="rounded-lg bg-zinc-100 px-2 py-1 text-[0.8125rem] font-semibold leading-6 text-zinc-900 hover:bg-zinc-200/80 active:text-zinc-900/70"
          >
            Sign out
          </a>
        <% else %>
          <a
            href="/sign-in"
            class="rounded-lg bg-zinc-100 px-2 py-1 text-[0.8125rem] font-semibold leading-6 text-zinc-900 hover:bg-zinc-200/80 active:text-zinc-900/70"
          >
            Sign In
          </a>
        <% end %>
      </div>
    </div>
  </div>
</nav>

<div class="py-10">
  <header>
    <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
      <h1 class="text-3xl font-bold leading-tight tracking-tight text-gray-900">
        Demo
      </h1>
    </div>
  </header>
  <main>
    <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
      <div class="px-4 py-8 sm:px-0">
        <div class="border-4 border-gray-200 border-dashed rounded-lg h-96"></div>
      </div>
    </div>
  </main>
</div>
