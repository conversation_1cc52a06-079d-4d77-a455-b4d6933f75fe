defmodule LiteCrmWeb.UserPdfController do
  use LiteCrmWeb, :controller
  # Removed unused alias
  alias LiteCrmWeb.{UserPdfComponent, PdfLayoutComponent}

  @doc """
  Generates the styles for the PDF.
  A lot of code here inspired by
  1. https://github.com/plastic-forks/beacon/blob/a9805adbef70be8550d2bdd04e862cbeff571268/lib/beacon/css_compiler.ex
  2. https://tailwindcss.com/docs/content-configuration#configuring-raw-content
  3. https://abulasar.com/adding-pdf-generate-feature-in-phoenix-liveview-app?utm_source=elixir-merge
  """
  def show(conn, %{"id" => id} = _params) do
    user = LiteCrm.Accounts.get_user!(id)
    conn = assign(conn, :user, user)
    {:ok, pdf} = to_pdf(conn.assigns)

    send_download(
      conn,
      {:binary, Base.decode64!(pdf)},
      content_type: "application/pdf",
      filename: "user-#{user.id}.pdf"
    )
  end

  defp to_pdf(assigns) do
    [
      content: content(assigns),
      size: :a4
    ]
    |> ChromicPDF.Template.source_and_options()
    |> ChromicPDF.print_to_pdf()
  end

  defp content(assigns) do
    inner_content = UserPdfComponent.render(assigns)

    config = ~s"""
    module.exports = {
      content: [
        { raw: `#{Enum.join(Phoenix.HTML.Safe.to_iodata(inner_content))}`, extension: 'html' },
      ]
    }
    """

    style =
      generate_styles(
        to_temp_file(
          config,
          "user.pdf.config"
        )
      )

    Phoenix.HTML.Safe.to_iodata(
      PdfLayoutComponent.render(%{inner_content: inner_content, style: style})
    )
  end

  defp generate_styles(tempfile) do
    Tailwind.install_and_run(:default, ~w(
      --config=#{tempfile}
      --output=#{generated_style_file_name()}
      --minify
    ))
    File.read!(generated_style_file_name())
  end

  defp to_temp_file(content, file_name) do
    dir = System.tmp_dir!()
    tmp_file = Path.join(dir, file_name)

    case File.write!(tmp_file, content) do
      :ok -> tmp_file
    end
  end

  defp generated_style_file_name(), do: Path.join(System.tmp_dir!(), "user-compiled.pdf.css")
end
