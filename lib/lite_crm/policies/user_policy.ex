defmodule LiteCrm.Policies.UserPolicy do
  @moduledoc """
  Policy for User authorization.
  """

  use Pundit.DefaultPolicy

  alias LiteCrm.Accounts.User

  def index?(%User{}, _) do
    true
  end

  def show?(%User{} = current_user, %User{} = user) do
    current_user.id == user.id || current_user.admin
  end

  def create?(%User{admin: true}, _) do
    true
  end

  def update?(%User{admin: true}, _) do
    true
  end

  def update?(%User{} = current_user, %User{} = user) do
    current_user.id == user.id
  end

  def delete?(%User{admin: true}, _) do
    true
  end

  def impersonate?(%User{admin: true}, _) do
    true
  end
end
