defmodule LiteCrm.Policies.LeadPolicy do
  @moduledoc """
  Policy for Lead authorization.
  """

  use Pundit.DefaultPolicy

  alias LiteCrm.Accounts.User
  alias LiteCrm.Crm.Lead

  def index?(%User{}, _) do
    true
  end

  def show?(%User{}, %Lead{}) do
    true
  end

  def create?(%User{}, _) do
    true
  end

  def update?(%User{}, %Lead{}) do
    true
  end

  def delete?(%User{}, %Lead{}) do
    true
  end
end
