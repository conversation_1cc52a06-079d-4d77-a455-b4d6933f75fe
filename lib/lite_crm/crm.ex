defmodule LiteCrm.Crm do
  @moduledoc """
  The Crm context.
  """

  import Ecto.Query, warn: false
  alias LiteCrm.Repo
  alias LiteCrm.Crm.Lead

  @doc """
  Returns the list of leads.

  ## Examples

      iex> list_leads()
      [%Lead{}, ...]

  """
  def list_leads(opts \\ []) do
    filter = Keyword.get(opts, :filter)
    order = Keyword.get(opts, :order, :updated_at)
    direction = Keyword.get(opts, :direction, :desc)
    page = Keyword.get(opts, :page, 1)
    per_page = Keyword.get(opts, :per_page, 20)

    query = from l in Lead, select: l

    query =
      if filter && filter != "" do
        filter_term = "%#{filter}%"

        from l in query,
          where: ilike(l.description, ^filter_term)
      else
        query
      end

    query =
      from l in query,
        order_by: [{^direction, ^order}],
        limit: ^per_page,
        offset: ^((page - 1) * per_page)

    Repo.all(query)
  end

  @doc """
  Gets the total count of leads.
  """
  def count_leads(filter \\ nil) do
    query = from l in Lead, select: count(l.id)

    query =
      if filter && filter != "" do
        filter_term = "%#{filter}%"

        from l in query,
          where: ilike(l.description, ^filter_term)
      else
        query
      end

    Repo.one(query)
  end

  @doc """
  Gets a single lead.

  Raises `Ecto.NoResultsError` if the Lead does not exist.

  ## Examples

      iex> get_lead!(123)
      %Lead{}

      iex> get_lead!(456)
      ** (Ecto.NoResultsError)

  """
  def get_lead!(id), do: Repo.get!(Lead, id)

  @doc """
  Creates a lead.

  ## Examples

      iex> create_lead(%{field: value})
      {:ok, %Lead{}}

      iex> create_lead(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_lead(attrs \\ %{}) do
    %Lead{}
    |> Lead.create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a lead.

  ## Examples

      iex> update_lead(lead, %{field: new_value})
      {:ok, %Lead{}}

      iex> update_lead(lead, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_lead(%Lead{} = lead, attrs) do
    lead
    |> Lead.update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a lead.

  ## Examples

      iex> delete_lead(lead)
      {:ok, %Lead{}}

      iex> delete_lead(lead)
      {:error, %Ecto.Changeset{}}

  """
  def delete_lead(%Lead{} = lead) do
    Repo.delete(lead)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking lead changes.

  ## Examples

      iex> change_lead(lead)
      %Ecto.Changeset{data: %Lead{}}

  """
  def change_lead(%Lead{} = lead, attrs \\ %{}) do
    Lead.changeset(lead, attrs)
  end
end
