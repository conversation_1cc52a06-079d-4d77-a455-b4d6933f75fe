defmodule LiteCrm.Crm.Lead do
  use Ecto.Schema
  import Ecto.Changeset

  @moduledoc """
  The Lead schema.
  """

  schema "leads" do
    field :description, :string

    timestamps()
  end

  @doc """
  Creates a lead changeset.
  """
  def changeset(lead, attrs) do
    lead
    |> cast(attrs, [:description])
  end

  @doc """
  Creates a changeset for creating a new lead.
  """
  def create_changeset(lead, attrs) do
    lead
    |> cast(attrs, [:description])
  end

  @doc """
  Creates a changeset for updating a lead.
  """
  def update_changeset(lead, attrs) do
    lead
    |> cast(attrs, [:description])
  end
end
