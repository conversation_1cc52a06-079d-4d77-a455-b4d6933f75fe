defmodule LiteCrm.UiSettings do
  use GenServer

  @moduledoc """
  ETS-based user UI settings store.
  """

  def start_link(opts) do
    GenServer.start_link(__MODULE__, opts, name: __MODULE__)
  end

  @impl true
  def init(_) do
    :ets.new(:ui_settings, [:named_table, read_concurrency: true])
    {:ok, []}
  end

  @impl true
  def handle_call({:lookup, key}, _from, state) do
    {:reply, :ets.lookup(:ui_settings, key), state}
  end

  @impl true
  def handle_call({:values, key}, _from, state) do
    {:reply, extract_values(key), state}
  end

  @impl true
  def handle_cast({:insert, key, value}, state) do
    :ets.insert(:ui_settings, {key, value})
    {:noreply, state}
  end

  @impl true
  def handle_cast({:toggle, key, value, nil}, state) do
    values = extract_values(key)

    if Enum.member?(values, value) do
      :ets.insert(:ui_settings, {key, List.delete(value, value)})
    else
      :ets.insert(:ui_settings, {key, values ++ [value]})
    end

    {:noreply, state}
  end

  @impl true
  def handle_cast({:toggle, key, new_values, added}, state) when is_list(new_values) do
    values = extract_values(key)

    if added do
      :ets.insert(:ui_settings, {key, values ++ new_values})
    else
      :ets.insert(:ui_settings, {key, values -- new_values})
    end

    {:noreply, state}
  end

  @impl true
  def handle_cast({:toggle, key, value, added}, state) do
    values = extract_values(key)

    if added do
      :ets.insert(:ui_settings, {key, values ++ [value]})
    else
      :ets.insert(:ui_settings, {key, List.delete(values, value)})
    end

    {:noreply, state}
  end

  @impl true
  def handle_cast({:delete, key}, state) do
    :ets.delete(:ui_settings, key)
    {:noreply, state}
  end

  defp extract_values(key),
    do: (Enum.at(:ets.lookup(:ui_settings, key), 0) || {nil, []}) |> elem(1)
end
