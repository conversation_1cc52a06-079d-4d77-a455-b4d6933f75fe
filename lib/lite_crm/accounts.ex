defmodule LiteCrm.Accounts do
  @moduledoc """
  The Accounts context.
  """

  import Ecto.Query, warn: false
  alias <PERSON>te<PERSON>rm.Repo
  alias LiteCrm.Accounts.{User, Token}

  ## Database getters

  @doc """
  Gets a user by email.

  ## Examples

      iex> get_user_by_email("<EMAIL>")
      %User{}

      iex> get_user_by_email("<EMAIL>")
      nil

  """
  def get_user_by_email(email) when is_binary(email) do
    Repo.get_by(User, email: email)
  end

  @doc """
  Gets a user by email and password.

  ## Examples

      iex> get_user_by_email_and_password("<EMAIL>", "correct_password")
      %User{}

      iex> get_user_by_email_and_password("<EMAIL>", "invalid_password")
      nil

  """
  def get_user_by_email_and_password(email, password)
      when is_binary(email) and is_binary(password) do
    user = Repo.get_by(User, email: email)
    if User.valid_password?(user, password), do: user
  end

  @doc """
  Gets a single user.

  Raises `Ecto.NoResultsError` if the User does not exist.

  ## Examples

      iex> get_user!(123)
      %User{}

      iex> get_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_user!(id), do: Repo.get!(User, id)

  ## User registration

  @doc """
  Registers a user.

  ## Examples

      iex> register_user(%{field: value})
      {:ok, %User{}}

      iex> register_user(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def register_user(attrs) do
    %User{}
    |> User.registration_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking user changes.

  ## Examples

      iex> change_user_registration(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_user_registration(%User{} = user, attrs \\ %{}) do
    User.registration_changeset(user, attrs, hash_password: false)
  end

  ## Settings

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the user email.

  ## Examples

      iex> change_user_email(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_user_email(user, attrs \\ %{}) do
    User.email_changeset(user, attrs)
  end

  @doc """
  Emulates that the email will change without actually changing
  it in the database.

  ## Examples

      iex> apply_user_email(user, "valid password", %{email: ...})
      {:ok, %User{}}

      iex> apply_user_email(user, "invalid password", %{email: ...})
      {:error, %Ecto.Changeset{}}

  """
  def apply_user_email(user, password, attrs) do
    user
    |> User.email_changeset(attrs)
    |> User.validate_current_password(password)
    |> Ecto.Changeset.apply_action(:update)
  end

  @doc """
  Updates the user email using the given token.

  If the token matches, the user email is updated and the token is deleted.
  The confirmed_at date is also updated to the current time.
  """
  def update_user_email(user, token) do
    context = "change:#{user.email}"

    with {:ok, query} <- verify_change_email_token_query(token, context),
         %Token{extra_data: %{"email" => email}} <- Repo.one(query),
         {:ok, _} <- Repo.transaction(user_email_multi(user, email, context)) do
      :ok
    else
      _ -> :error
    end
  end

  defp user_email_multi(user, email, context) do
    changeset =
      user
      |> User.email_changeset(%{email: email})

    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, changeset)
    |> Ecto.Multi.delete_all(:tokens, Token.by_user_and_contexts_query(user, [context]))
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the user password.

  ## Examples

      iex> change_user_password(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_user_password(user, attrs \\ %{}) do
    User.password_changeset(user, attrs, hash_password: false)
  end

  @doc """
  Updates the user password.

  ## Examples

      iex> update_user_password(user, "valid password", %{password: ...})
      {:ok, %User{}}

      iex> update_user_password(user, "invalid password", %{password: ...})
      {:error, %Ecto.Changeset{}}

  """
  def update_user_password(user, password, attrs) do
    changeset =
      user
      |> User.password_changeset(attrs)
      |> User.validate_current_password(password)

    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, changeset)
    |> Ecto.Multi.delete_all(:tokens, Token.by_user_and_contexts_query(user, ["session"]))
    |> Repo.transaction()
    |> case do
      {:ok, %{user: user}} -> {:ok, user}
      {:error, :user, changeset, _} -> {:error, changeset}
    end
  end

  ## Session

  @doc """
  Generates a session token.
  """
  def generate_user_session_token(user) do
    {token, token_data} = build_session_token(user)
    Repo.insert!(token_data)
    token
  end

  defp build_session_token(user) do
    token = :crypto.strong_rand_bytes(32)
    jti = Base.url_encode64(token, padding: false)

    {token,
     %Token{
       jti: jti,
       subject: to_string(user.id),
       purpose: "session",
       expires_at: expiry_datetime("session")
     }}
  end

  defp expiry_datetime(type) do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    case type do
      "session" -> DateTime.add(now, 60 * 60 * 24 * 60, :second)
      "email" -> DateTime.add(now, 24 * 3600, :second)
    end
  end

  @doc """
  Gets the user with the given signed token.
  """
  def get_user_by_session_token(token) when is_binary(token) do
    jti = Base.url_encode64(token, padding: false)
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    query =
      from t in Token,
        where: t.jti == ^jti and t.purpose == "session" and t.expires_at > ^now,
        join: u in User,
        on: u.id == fragment("CAST(? AS INTEGER)", t.subject),
        select: u

    Repo.one(query)
  end

  def get_user_by_session_token(_), do: nil

  @doc """
  Deletes the signed token with the given context.
  """
  def delete_user_session_token(token) do
    jti = Base.url_encode64(token, padding: false)
    Repo.delete_all(from t in Token, where: t.jti == ^jti and t.purpose == "session")
    :ok
  end

  @doc """
  Verifies a change email token.
  """
  def verify_change_email_token_query(token, context) do
    jti = Base.url_encode64(token, padding: false)
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    query =
      from t in Token,
        where: t.jti == ^jti and t.purpose == ^context and t.expires_at > ^now

    {:ok, query}
  end

  @doc """
  Delivers the confirmation instructions to the user.
  """
  def deliver_user_confirmation_instructions(user, confirmation_url_fun) do
    # In a real app, you'd send an email here
    {:ok, %{to: user.email, body: confirmation_url_fun.("fake_token")}}
  end

  @doc """
  Delivers the reset password instructions to the user.
  """
  def deliver_user_reset_password_instructions(%User{} = user, reset_password_url_fun) do
    {token, token_data} = build_email_token(user, "reset_password", %{"email" => user.email})
    Repo.insert!(token_data)

    # In a real app, you'd send an email here
    {:ok, %{to: user.email, token: token, body: reset_password_url_fun.(token)}}
  end

  @doc """
  Gets the user by reset password token.
  """
  def get_user_by_reset_password_token(token) do
    with {:ok, query} <- verify_reset_password_token_query(token),
         %Token{extra_data: %{"email" => email}} <- Repo.one(query),
         %User{} = user <- get_user_by_email(email) do
      user
    else
      _ -> nil
    end
  end

  defp verify_reset_password_token_query(token) do
    jti = Base.url_encode64(token, padding: false)
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    query =
      from t in Token,
        where: t.jti == ^jti and t.purpose == "reset_password" and t.expires_at > ^now

    {:ok, query}
  end

  @doc """
  Resets the user password.
  """
  def reset_user_password(user, attrs) do
    Ecto.Multi.new()
    |> Ecto.Multi.update(:user, User.password_changeset(user, attrs))
    |> Ecto.Multi.delete_all(:tokens, Token.by_user_and_contexts_query(user, ["reset_password"]))
    |> Repo.transaction()
    |> case do
      {:ok, %{user: user}} -> {:ok, user}
      {:error, :user, changeset, _} -> {:error, changeset}
    end
  end

  @doc """
  Delivers the email change instructions to the user.
  """
  def deliver_user_update_email_instructions(user, current_email, update_email_url_fun) do
    {token, token_data} =
      build_email_token(user, "change:#{current_email}", %{"email" => current_email})

    Repo.insert!(token_data)

    # In a real app, you'd send an email here
    {:ok, %{to: current_email, body: update_email_url_fun.(token)}}
  end

  defp build_email_token(user, context, extra_data) do
    token = :crypto.strong_rand_bytes(32)
    jti = Base.url_encode64(token, padding: false)

    {token,
     %Token{
       jti: jti,
       subject: to_string(user.id),
       purpose: context,
       extra_data: extra_data,
       expires_at: expiry_datetime("email")
     }}
  end

  # Moved to the combined expiry_datetime/1 function above

  @doc """
  Gets all users.
  """
  def list_users(opts \\ []) do
    filter = Keyword.get(opts, :filter)
    order = Keyword.get(opts, :order, :updated_at)
    direction = Keyword.get(opts, :direction, :desc)
    page = Keyword.get(opts, :page, 1)
    per_page = Keyword.get(opts, :per_page, 20)

    query = from u in User, select: u

    query =
      if filter && filter != "" do
        filter_term = "%#{filter}%"

        from u in query,
          where:
            ilike(u.first_name, ^filter_term) or ilike(u.last_name, ^filter_term) or
              ilike(u.email, ^filter_term)
      else
        query
      end

    query =
      from u in query,
        order_by: [{^direction, ^order}],
        limit: ^per_page,
        offset: ^((page - 1) * per_page)

    Repo.all(query)
  end

  @doc """
  Gets the total count of users.
  """
  def count_users(filter \\ nil) do
    query = from u in User, select: count(u.id)

    query =
      if filter && filter != "" do
        filter_term = "%#{filter}%"

        from u in query,
          where:
            ilike(u.first_name, ^filter_term) or ilike(u.last_name, ^filter_term) or
              ilike(u.email, ^filter_term)
      else
        query
      end

    Repo.one(query)
  end

  @doc """
  Updates a user.
  """
  def update_user(%User{} = user, attrs) do
    user
    |> User.update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Returns a list of all user IDs.
  """
  def list_all_user_ids do
    Repo.all(from u in User, select: u.id)
  end
end
