defmodule LiteCrm.Accounts.Token do
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  @moduledoc """
  The Token schema for user authentication.
  """

  @primary_key {:jti, :string, []}
  schema "tokens" do
    field :subject, :string
    field :expires_at, :utc_datetime
    field :purpose, :string
    field :extra_data, :map
    field :sent_to, :string

    timestamps(inserted_at: :created_at)
  end

  @doc """
  Creates a token changeset.
  """
  def changeset(token, attrs) do
    token
    |> cast(attrs, [:jti, :subject, :expires_at, :purpose, :extra_data, :sent_to])
    |> validate_required([:jti, :subject, :expires_at, :purpose])
  end

  @doc """
  Builds a query to get all tokens for a user with a given context.
  """
  def by_user_and_contexts_query(user, contexts) do
    from t in __MODULE__,
      where: t.subject == ^to_string(user.id) and t.purpose in ^contexts
  end
end
