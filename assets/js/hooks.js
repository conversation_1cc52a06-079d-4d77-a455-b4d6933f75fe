import * as mdc from '../vendor/material-components-web.min.js';

let Hooks = {};

Hooks.MaterialAside = {
  mounted() {
    let drawer = mdc.drawer.MDCDrawer.attachTo(this.el);
    this.handleEvent("toggle-drawer", () => (drawer.open = !drawer.open));
  },
};
Hooks.MaterialTopAppBar = {
  mounted() {
    const topAppBar = mdc.topAppBar.MDCTopAppBar.attachTo(this.el);
    topAppBar.setScrollTarget(document.getElementById("main-content"));
    topAppBar.listen("MDCTopAppBar:nav", () => {
      this.pushEvent("toggle-drawer", {});
    });
  },
};
Hooks.MaterialMenuButton = {
  mounted() {
    mdc.ripple.MDCRipple.attachTo(this.el);
    this.menu = mdc.menu.MDCMenu.attachTo(
      this.el.querySelector(".mdc-menu-surface"),
    );
    const button = this.el;
    this.menu.setAnchorElement(button);
    this.menu.setAnchorCorner(mdc.menu.Corner.BOTTOM_START);
    this.menu.setFixedPosition(true);
    button.addEventListener("click", () => (this.menu.open = !this.menu.open));
  },
};
Hooks.MaterialRipple = {
  mounted() {
    mdc.ripple.MDCRipple.attachTo(this.el);
  },
};
Hooks.MaterialTextField = {
  mounted() {
    mdc.textField.MDCTextField.attachTo(this.el);
  },
};
Hooks.MaterialDataTable = {
  mounted() {
    this.dataTable = mdc.dataTable.MDCDataTable.attachTo(this.el);
    if (this.el.dataset.allSelected === "true") {
      this.dataTable.foundation.adapter.setHeaderRowCheckboxChecked(true);
    }
    if (this.el.dataset.someSelected === "true") {
      this.dataTable.foundation.adapter.setHeaderRowCheckboxIndeterminate(true);
    }
    this.dataTable.listen("MDCDataTable:sorted", (event) => {
      this.pushEvent("material-data-table-sorted", {
        order: event.detail.columnId,
        reverse: event.detail.sortValue === "descending" ? true : false,
      });
    });
    this.dataTable.listen("MDCDataTable:rowSelectionChanged", (event) => {
      this.pushEvent("material-data-table-row-selected", {
        selected: event.detail.selected,
        row_id: event.detail.rowId,
      });
    });
    this.dataTable.listen("MDCDataTable:selectedAll", () => {
      this.pushEvent("material-data-table-all-selected", {});
    });
    this.dataTable.listen("MDCDataTable:unselectedAll", () => {
      this.pushEvent("material-data-table-all-unselected", {});
    });
  },
};
Hooks.MaterialSelect = {
  mounted() {
    this.select = mdc.select.MDCSelect.attachTo(this.el);
    this.select.listen("MDCSelect:change", (event) => {
      this.select.root.dispatchEvent(
        new CustomEvent("submit-now", {
          bubbles: true,
          cancelable: true,
          detail: {
            value: event.detail.value,
          },
        }),
      );
    });
  },
};
Hooks.SubmitNow = {
  mounted() {
    this.el.addEventListener("submit-now", () => {
      this.el.submit();
    });
  },
};
Hooks.PerPageForm = {
  mounted() {
    this.el.addEventListener("submit-now", (event) => {
      this.pushEvent("per_page_changed", { per_page: event.detail.value });
    });
  },
};
Hooks.MaterialSnackbar = {
  mounted() {
    this.snackbar = mdc.snackbar.MDCSnackbar.attachTo(this.el);
    this.snackbar.open();
  },
};
Hooks.MaterialTabBar = {
  mounted() {
    this.tabBar = mdc.tabBar.MDCTabBar.attachTo(this.el);
  },
};
Hooks.MaterialModal = {
  mounted() {
    this.modal = mdc.dialog.MDCDialog.attachTo(this.el);
    this.modal.listen("MDCDialog:closed", (event) => {
      this.pushEvent("modal-closed", { action: event.detail.action, id: this.el.id });
    });
    this.modal.listen("open-modal", (event) => {
      if (this.modal.root.id === event.detail.id) {
        this.modal.open();
      }
    });
    this.modal.listen("close-modal", (event) => {
      if (this.modal.root.id === event.detail.id) {
        this.modal.close();
      }
    });
    if(this.el.dataset.opened) {
      this.modal.open();
    }
  }
}

export default Hooks;
