/* default is to use prefers-color-scheme */
/* if you want dark mode via class: */
@variant dark (&:where(.dark, .dark *));

@variant phx-click-loading (.phx-click-loading&, .phx-click-loading &);
@variant phx-submit-loading (.phx-submit-loading&, .phx-submit-loading &);
@variant phx-change-loading (.phx-change-loading&, .phx-change-loading &);

@plugin "../heroicons.tailwind.plugin.js";

@import "tailwindcss" source("../..");

@import url("https://fonts.googleapis.com/icon?family=Material+Icons");
@import "../vendor/material-components-web.min.css";

/* This file is for your main application CSS */

@theme {
    --color-primary: "#0d4c92";
    --color-secondary: "#97a17a";
    --color-danger: "#f44336";
    --color-grayish: "#c0c2c5";

    --color-congress-blue-50: "#f1f7fe";
    --color-congress-blue-100: "#e2eefc";
    --color-congress-blue-200: "#bedbf9";
    --color-congress-blue-300: "#85bdf4";
    --color-congress-blue-400: "#459beb";
    --color-congress-blue-500: "#1c7fdb";
    --color-congress-blue-600: "#0f63ba";
    --color-congress-blue-700: "#0d4c92";
    --color-congress-blue-800: "#0f437d";
    --color-congress-blue-900: "#123968";
    --color-congress-blue-950: "#0c2445";

    --color-moon-mist-50: "#f4f5f0";
    --color-moon-mist-100: "#e6e9de";
    --color-moon-mist-200: "#d6dac8";
    --color-moon-mist-300: "#b3bb9b";
    --color-moon-mist-400: "#97a17a";
    --color-moon-mist-500: "#7a855d";
    --color-moon-mist-600: "#5f6947";
    --color-moon-mist-700: "#4a5239";
    --color-moon-mist-800: "#3d4331";
    --color-moon-mist-900: "#363a2d";
    --color-moon-mist-950: "#1b1e15";

    --color-pomegranate-50: "#fef3f2";
    --color-pomegranate-100: "#ffe3e1";
    --color-pomegranate-200: "#ffccc8";
    --color-pomegranate-300: "#ffa8a2";
    --color-pomegranate-400: "#fc776d";
    --color-pomegranate-500: "#f44336";
    --color-pomegranate-600: "#e22d20";
    --color-pomegranate-700: "#be2217";
    --color-pomegranate-800: "#9d2017";
    --color-pomegranate-900: "#82211a";
}

@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,600;1,400;1,600&display=swap");

html,
body,
label {
    font-family: "Roboto", sans-serif;
}

html,
body {
    scroll-behavior: smooth;
}

html [id],
body [id] {
    scroll-margin: 120px !important;
}

:host,
:root {
    --real-primary: #0d4c92;
    --real-secondary: #97a17a;
    --real-warn: rgb(244, 67, 54);

    --mdc-theme-primary: var(--real-primary);
    --mdc-theme-secondary: var(--real-secondary);
    --mdc-dialog-z-index: 100;
    --mdc-ripple-color: white;
}

/* https://github.com/rmwc/rmwc/blob/6fb44762fb4665cd05907de3bcd097e03ae3cdda/packages/theme/src/theme.css#L84 */

.mdc-select:not(.mdc-select--disabled):not(
        .mdc-select--invalid
    ).mdc-select--focused
    .mdc-floating-label {
    color: var(--real-primary);
}

.mdc-text-field:not(.mdc-text-field--disabled):not(
        .mdc-text-field--invalid
    ).mdc-text-field--focused
    .mdc-floating-label {
    color: var(--real-secondary);
}

.turbo-progress-bar {
    background-color: #77a1d6;
    filter: brightness(90%);
}

.mdc-button.menu-item {
    --mdc-theme-primary: white;
}

.mdc-button--accent:not(.mdc-button--raised):not([disabled]) {
    color: #77a1d6;
    color: var(--real-secondary, #77a1d6);
}

.mdc-button--accent.mdc-button--raised:not([disabled]) {
    background-color: #77a1d6;
    background-color: var(--real-secondary, #77a1d6);
}

.mdc-button.mdc-button--accent .mdc-button__ripple::before,
.mdc-button.mdc-button--accent .mdc-button__ripple::after {
    background-color: #77a1d6;
    background-color: var(--real-secondary, #77a1d6);
}

.mdc-button--warn:not(.mdc-button--raised):not([disabled]) {
    color: rgb(244, 67, 54);
    color: var(--real-warn, rgb(244, 67, 54));
}

.mdc-button--warn.mdc-button--raised:not([disabled]) {
    background-color: rgb(244, 67, 54);
    background-color: var(--real-warn, rgb(244, 67, 54));
}

.mdc-button.mdc-button--warn .mdc-button__ripple::before,
.mdc-button.mdc-button--warn .mdc-button__ripple::after {
    background-color: rgb(244, 67, 54);
    background-color: var(--real-warn, rgb(244, 67, 54));
}

.mdc-button--white:not(.mdc-button--raised):not([disabled]) {
    color: white;
}

.mdc-button--white.mdc-button--raised:not([disabled]) {
    background-color: white;
}

.mdc-button.mdc-button--white .mdc-button__ripple::before,
.mdc-button.mdc-button--white .mdc-button__ripple::after {
    background-color: white;
}

.mdc-text-field:not(.mdc-text-field--label-floating) .iti__flag-container {
    display: none !important;
}

.logo-image {
    display: block;
    width: 80px;
    min-width: 80px;
    line-height: 36px;
}

.mdc-drawer.main-drawer .mdc-deprecated-list-item {
    color: #c0c2c5;
}

.mdc-drawer.main-drawer
    .mdc-deprecated-list-item
    .mdc-deprecated-list-item__graphic {
    color: #c0c2c5;
    fill: #c0c2c5;
}

.mdc-drawer.main-drawer .mdc-deprecated-list-item--activated {
    background-color: var(--real-secondary);
    color: var(--real-primary);
}

.mdc-drawer.main-drawer
    .mdc-deprecated-list-item--activated
    .mdc-deprecated-list-item__graphic {
    color: var(--real-primary);
}

.mdc-drawer .mdc-deprecated-list-item {
    margin: 0;
    border-radius: 0;
}

aside.mdc-drawer:not(.mdc-drawer--right) {
    box-shadow:
        0px 5px 5px -3px rgb(0 0 0 / 20%),
        0px 8px 10px 1px rgb(0 0 0 / 14%),
        0px 3px 14px 2px rgb(0 0 0 / 12%);
}

aside.mdc-drawer.main-drawer {
    background-color: #2d323e;
}

.mdc-drawer--right.mdc-drawer--dismissible {
    right: 0 !important;
    left: initial !important;
}

.mdc-drawer--right.mdc-drawer.mdc-drawer--open:not(.mdc-drawer--closing)
    + .mdc-drawer-app-content {
    margin-right: 16rem !important;
    margin-left: 0 !important;
}

.mdc-drawer--right.mdc-drawer.mdc-drawer--open.mdc-drawer--closing
    + .mdc-drawer-app-content {
    transition: margin-right 200ms 15ms linear;
}

.mdc-drawer--right.mdc-drawer.mdc-drawer--open.mdc-drawer--openening.mdc-drawer--closing
    + .mdc-drawer-app-content {
    transition: margin-right 200ms 15ms linear;
}

.mdc-drawer--right.mdc-drawer--animate {
    transform: translateX(100%) !important;
}

.mdc-drawer--right.mdc-drawer--closing {
    transform: translateX(100%) !important;
}

.mdc-drawer--right.mdc-drawer--opening {
    transform: translateX(0) !important;
}

.mdc-dialog__title::before {
    content: none !important;
}

.mdc-dialog__title {
    padding-top: 1rem;
}

.clip-path-bottom {
    clip-path: inset(-8px -8px 0px -8px);
}

.clip-path-top {
    clip-path: inset(0px -8px -8px -8px);
}

.mdc-drawer.mdc-drawer--open:not(.mdc-drawer--closing)
    + .mdc-drawer-app-content
    .mdc-top-app-bar {
    width: calc(100vw - 16rem);
}

.mdc-text-field .field_with_errors {
    width: 100%;
}

.environment-banner .label {
    text-transform: uppercase;
    color: white;
}

.environment-banner.development {
    background-color: cornflowerblue;
}

.environment-banner.qa {
    background-color: #a1309f;
}

.environment-banner.staging {
    background-color: #f09201;
}

.bg-secondary .mdc-tab-indicator .mdc-tab-indicator__content--underline {
    border-color: white !important;
}

.bg-secondary .mdc-tab--active .mdc-tab__text-label {
    color: white !important;
}

.bg-secondary .mdc-tab .mdc-tab__icon {
    color: white !important;
}

.bg-secondary .mdc-tab--active .mdc-tab__icon {
    color: white !important;
}

.bg-secondary .mdc-tab:not(.mdc-tab--active) .mdc-tab__text-label {
    color: white !important;
}

.mdc-chip.red {
    background-color: #f44336 !important;
    color: white !important;
}

.mdc-chip.red .mdc-chip__icon {
    color: white !important;
}

.mdc-notched-outline__leading {
    border-right-style: none !important;
}

.mdc-notched-outline__notch {
    border-left-style: none !important;
    border-right-style: none !important;
}

.mdc-notched-outline__trailing {
    border-left-style: none !important;
}

.mdc-dialog .mdc-dialog__title.bg-secondary .mdc-dialog__close {
    color: white !important;
}
