/* This is your custom storybook stylesheet. */
/* default is to use prefers-color-scheme */
/* if you want dark mode via class: */
@variant dark (&:where(.dark, .dark *));

@variant phx-click-loading (.phx-click-loading&, .phx-click-loading &);
@variant phx-submit-loading (.phx-submit-loading&, .phx-submit-loading &);
@variant phx-change-loading (.phx-change-loading&, .phx-change-loading &);

@plugin "../heroicons.tailwind.plugin.js";

@import "tailwindcss" source("../..");



/*
 * Put your component styling within the Tailwind utilities layer.
 * See the https://hexdocs.pm/phoenix_storybook/sandboxing.html guide for more info.
 */

@layer utilities {
    @import "../vendor/material-components-web.min.css";
    @import url("https://fonts.googleapis.com/icon?family=Material+Icons");
    * {
        font-family: system-ui;
    }

    /* Material Design Variables */
    :host,
    :root {
        --real-primary: #0d4c92;
        --real-secondary: #97a17a;
        --real-warn: rgb(244, 67, 54);

        --mdc-theme-primary: var(--real-primary);
        --mdc-theme-secondary: var(--real-secondary);
        --mdc-dialog-z-index: 100;
        --mdc-ripple-color: white;
    }

    /* Material Design Component Overrides */
    .mdc-select:not(.mdc-select--disabled):not(
            .mdc-select--invalid
        ).mdc-select--focused
        .mdc-floating-label {
        color: var(--real-primary);
    }

    .mdc-text-field:not(.mdc-text-field--disabled):not(
            .mdc-text-field--invalid
        ).mdc-text-field--focused
        .mdc-floating-label {
        color: var(--real-secondary);
    }

    .mdc-button.menu-item {
        --mdc-theme-primary: white;
    }

    .mdc-button--accent:not(.mdc-button--raised):not([disabled]) {
        color: #77a1d6;
        color: var(--real-secondary, #77a1d6);
    }

    .mdc-notched-outline__notch {
        border-left-style: none !important;
        border-right-style: none !important;
    }

    .mdc-notched-outline__trailing {
        border-left-style: none !important;
    }

    .mdc-dialog .mdc-dialog__title.bg-secondary .mdc-dialog__close {
        color: white !important;
    }
}
