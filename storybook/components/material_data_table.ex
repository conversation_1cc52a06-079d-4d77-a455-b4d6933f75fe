defmodule Storybook.Components.MaterialDataTable do
  use Phoenix.Component
  import Phoenix.HTML.Form

  @doc """
  A simplified version of the material_data_table component for storybook
  """
  attr :name, :string, required: true
  attr :id, :string, required: true

  slot :header do
    attr :label, :string, required: true
    attr :sort_value, :string, required: true
    attr :column_id, :string, required: true
  end

  slot :table_body

  def material_data_table(assigns) do
    ~H"""
    <div class="mdc-data-table w-full" id={@id}>
      <div class="mdc-data-table__table-container">
        <table class="mdc-data-table__table w-full" aria-label={@name}>
          <thead>
            <tr class="mdc-data-table__header-row">
              <th
                class="mdc-data-table__header-cell mdc-data-table__header-cell--checkbox"
                role="columnheader"
                scope="col"
              >
                <div class="mdc-checkbox mdc-data-table__header-row-checkbox">
                  <input
                    type="checkbox"
                    class="mdc-checkbox__native-control"
                    aria-label="Toggle all rows"
                  />
                  <div class="mdc-checkbox__background">
                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                      <path
                        class="mdc-checkbox__checkmark-path"
                        fill="none"
                        d="M1.73,12.91 8.1,19.28 22.79,4.59"
                      />
                    </svg>
                    <div class="mdc-checkbox__mixedmark"></div>
                  </div>
                  <div class="mdc-checkbox__ripple"></div>
                </div>
              </th>
              <%= for header <- @header do %>
                <th
                  class="mdc-data-table__header-cell mdc-data-table__header-cell--with-sort"
                  role="columnheader"
                  scope="col"
                  aria-sort={header.sort_value}
                  data-column-id={header.column_id}
                >
                  <div class="mdc-data-table__header-cell-wrapper">
                    <div class="mdc-data-table__header-cell-label">{header.label}</div>
                    <button
                      class="mdc-icon-button material-icons mdc-data-table__sort-icon-button"
                      aria-label={"Sort by #{header.label}"}
                      aria-describedby={"#{header.column_id}-label"}
                    >
                      arrow_upward
                    </button>
                    <div
                      class="mdc-data-table__sort-status-label"
                      aria-hidden="true"
                      id={"#{header.column_id}-label"}
                    >
                    </div>
                  </div>
                </th>
              <% end %>
            </tr>
          </thead>
          <tbody class="mdc-data-table__content">
            {render_slot(@table_body)}
          </tbody>
        </table>
      </div>
      <div class="mdc-data-table__pagination">
        <div class="mdc-data-table__pagination-trailing">
          <div class="mdc-data-table__pagination-rows-per-page">
            <div class="mdc-data-table__pagination-rows-per-page-label">
              Rows per page
            </div>

            <div class="mdc-select mdc-select--outlined mdc-data-table__pagination-rows-per-page-select">
              <div class="mdc-select__anchor">
                <span class="mdc-notched-outline">
                  <span class="mdc-notched-outline__leading"></span>
                  <span class="mdc-notched-outline__trailing"></span>
                </span>
                <span class="mdc-select__selected-text">20</span>
                <span class="mdc-select__dropdown-icon">
                  <svg
                    class="mdc-select__dropdown-icon-graphic"
                    viewBox="7 10 10 5"
                    focusable="false"
                  >
                    <polygon
                      class="mdc-select__dropdown-icon-inactive"
                      stroke="none"
                      fill-rule="evenodd"
                      points="7 10 12 15 17 10"
                    >
                    </polygon>
                    <polygon
                      class="mdc-select__dropdown-icon-active"
                      stroke="none"
                      fill-rule="evenodd"
                      points="7 15 12 10 17 15"
                    >
                    </polygon>
                  </svg>
                </span>
              </div>
            </div>

            <div class="mdc-data-table__pagination-navigation">
              <div class="mdc-data-table__pagination-total">
                1‑10 of 100
              </div>
              <button
                class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                data-first-page="true"
                disabled
              >
                <div class="mdc-button__icon">first_page</div>
              </button>
              <button
                class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                data-prev-page="true"
                disabled
              >
                <div class="mdc-button__icon">chevron_left</div>
              </button>
              <button
                class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                data-next-page="true"
              >
                <div class="mdc-button__icon">chevron_right</div>
              </button>
              <button
                class="mdc-icon-button material-icons mdc-data-table__pagination-button"
                data-last-page="true"
              >
                <div class="mdc-button__icon">last_page</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end
end
