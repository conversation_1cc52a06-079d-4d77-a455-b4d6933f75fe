defmodule Storybook.Examples.MaterialComponents do
  use PhoenixStorybook.Story, :example
  import LiteCrmWeb.MaterialComponents

  alias Phoenix.LiveView.JS

  def doc do
    "An example of what you can achieve with Material components."
  end

  defstruct [:id, :first_name, :last_name, :email, :role]

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       drawer_disabled: false,
       current_url: "/users",
       users: [
         %__MODULE__{
           id: 1,
           first_name: "<PERSON>",
           last_name: "<PERSON><PERSON>",
           email: "<EMAIL>",
           role: "Admin"
         },
         %__MODULE__{
           id: 2,
           first_name: "<PERSON>",
           last_name: "<PERSON><PERSON><PERSON><PERSON>",
           email: "<EMAIL>",
           role: "User"
         },
         %__MODULE__{
           id: 3,
           first_name: "<PERSON>",
           last_name: "<PERSON>",
           email: "<EMAIL>",
           role: "User"
         }
       ]
     )}
  end

  @impl true
  def handle_event("toggle-drawer", _, socket) do
    {:noreply, assign(socket, drawer_disabled: !socket.assigns.drawer_disabled)}
  end

  @impl true
  def handle_event("open-modal", %{"id" => id}, socket) do
    {:noreply, push_event(socket, "open-modal", %{id: id})}
  end

  @impl true
  def handle_event("modal-closed", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="flex flex-col h-screen">
      <aside id="main" disabled={@drawer_disabled}>
        <.header>
          <div class="text-white font-extrabold tracking-tight">LiteCRM</div>
        </.header>
        <:sidebar_menu>
          <.sidebar_menu>
            <:link href="#" current={false} icon="dashboard" id="dashboard">
              Dashboard
            </:link>
            <:link href="#" current={true} icon="group" id="users">
              Users
            </:link>
            <:link href="#" current={false} icon="settings" id="settings">
              Settings
            </:link>
          </.sidebar_menu>
        </:sidebar_menu>
      </aside>

      <.top_app_bar
        id="top-app-bar"
        drawer_disabled={@drawer_disabled}
        current_user={%{email: "<EMAIL>", name: "John Doe"}}
      >
        <:heading>Material Components Demo</:heading>
      </.top_app_bar>

      <main class="p-6 mt-16">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold">Users</h1>
          <button
            class="mdc-button mdc-button--raised"
            phx-click="open-modal"
            phx-value-id="new-user-modal"
          >
            <span class="mdc-button__ripple"></span>
            <span class="mdc-button__label">Add User</span>
          </button>
        </div>

        <.material_data_table id="users-table" name="Users">
          <:header label="ID" sort_value="id" column_id="id" />
          <:header label="First Name" sort_value="first_name" column_id="first_name" />
          <:header label="Last Name" sort_value="last_name" column_id="last_name" />
          <:header label="Email" sort_value="email" column_id="email" />
          <:header label="Role" sort_value="role" column_id="role" />
          <:table_body>
            <%= for user <- @users do %>
              <tr class="mdc-data-table__row" data-row-id={"user-#{user.id}"}>
                <td class="mdc-data-table__cell">{user.id}</td>
                <td class="mdc-data-table__cell">{user.first_name}</td>
                <td class="mdc-data-table__cell">{user.last_name}</td>
                <td class="mdc-data-table__cell">{user.email}</td>
                <td class="mdc-data-table__cell">{user.role}</td>
              </tr>
            <% end %>
          </:table_body>
        </.material_data_table>
      </main>

      <.modal id="new-user-modal" title="Add New User" cancel_button="Cancel" ok_button="Save">
        <:content>
          <div class="space-y-4">
            <.material_input id="first-name" label="First Name" style={:outlined} />
            <.material_input id="last-name" label="Last Name" style={:outlined} />
            <.material_input id="email" label="Email" style={:outlined} />
            <.material_select id="role" name="role" label="Role" style={:outlined}>
              <:option value="user" label="User" />
              <:option value="admin" label="Admin" />
            </.material_select>
          </div>
        </:content>
      </.modal>
    </div>
    """
  end
end
