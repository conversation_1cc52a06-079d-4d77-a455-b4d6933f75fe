defmodule Storybook.MaterialComponents do
  use PhoenixStorybook.Index

  def folder_icon, do: {:fa, "layer-group", :light, "psb-mr-1"}
  def folder_name, do: "Material Components"

  def entry("material_components/modal") do
    [
      name: "Modal",
      icon: {:fa, "window-maximize", :thin}
    ]
  end

  def entry("material_components/aside") do
    [
      name: "Aside",
      icon: {:fa, "sidebar", :thin}
    ]
  end

  def entry("material_components/top_app_bar") do
    [
      name: "Top App Bar",
      icon: {:fa, "rectangle-wide", :thin}
    ]
  end

  def entry("material_components/menu_button") do
    [
      name: "<PERSON><PERSON>",
      icon: {:fa, "bars", :thin}
    ]
  end

  def entry("material_components/sidebar_menu") do
    [
      name: "Sidebar Menu",
      icon: {:fa, "list", :thin}
    ]
  end

  def entry("material_components/material_input") do
    [
      name: "Input",
      icon: {:fa, "input-text", :thin}
    ]
  end

  def entry("material_components/material_select") do
    [
      name: "Select",
      icon: {:fa, "square-caret-down", :thin}
    ]
  end

  def entry("material_components/material_data_table") do
    [
      name: "Data Table",
      icon: {:fa, "table", :thin}
    ]
  end
end
