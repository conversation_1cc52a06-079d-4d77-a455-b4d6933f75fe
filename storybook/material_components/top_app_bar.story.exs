defmodule Storybook.MaterialComponents.TopAppBar do
  use PhoenixStorybook.Story, :component
  alias LiteCrmWeb.MaterialComponents

  def function, do: &MaterialComponents.top_app_bar/1
  def imports, do: [{MaterialComponents, []}]

  def variations do
    [
      %Variation{
        id: :default,
        attributes: %{
          id: "default-top-app-bar",
          current_user: %{email: "<EMAIL>", name: "<PERSON>"}
        },
        slots: [
          "<:heading>LiteCRM</:heading>"
        ]
      },
      %Variation{
        id: :with_admin,
        attributes: %{
          id: "admin-top-app-bar",
          current_user: %{email: "<EMAIL>", name: "<PERSON>"},
          current_admin_user: %{email: "<EMAIL>", name: "Admin User"}
        },
        slots: [
          "<:heading>LiteCRM Admin</:heading>"
        ]
      },
      %Variation{
        id: :drawer_disabled,
        attributes: %{
          id: "disabled-drawer-top-app-bar",
          current_user: %{email: "<EMAIL>", name: "<PERSON>"},
          drawer_disabled: true
        },
        slots: [
          "<:heading>LiteCRM (No Drawer)</:heading>"
        ]
      }
    ]
  end
end
