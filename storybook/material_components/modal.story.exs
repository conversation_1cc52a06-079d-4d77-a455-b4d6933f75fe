defmodule Storybook.MaterialComponents.Modal do
  use PhoenixStorybook.Story, :component
  alias LiteCrmWeb.MaterialComponents

  def function, do: &MaterialComponents.modal/1
  def imports, do: [{MaterialComponents, [hide_modal: 1, show_modal: 1]}]

  def template do
    """
    <div class="mb-4">
      <button class="mdc-button mdc-button--raised" phx-click={show_modal(":variation_id")}>
        <span class="mdc-button__ripple"></span>
        <span class="mdc-button__label">Open Modal</span>
      </button>
    </div>
    <.psb-variation/>
    """
  end

  def variations do
    [
      %Variation{
        id: :default,
        attributes: %{
          id: "default-modal",
          title: "Default Modal",
          cancel_button: "Cancel",
          ok_button: "OK"
        },
        slots: [
          "<:content>This is a default modal with standard buttons.</:content>"
        ]
      },
      %Variation{
        id: :custom_buttons,
        attributes: %{
          id: "custom-buttons-modal",
          title: "Custom Buttons Modal",
          cancel_button: "Dismiss",
          ok_button: "Confirm"
        },
        slots: [
          "<:content>This modal has custom button labels.</:content>"
        ]
      },
      %Variation{
        id: :with_additional_classes,
        attributes: %{
          id: "styled-modal",
          title: "Styled Modal",
          additional_classes: "w-full max-w-3xl"
        },
        slots: [
          "<:content>This modal has additional styling classes applied.</:content>"
        ]
      }
    ]
  end
end
