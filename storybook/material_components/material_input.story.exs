defmodule Storybook.MaterialComponents.MaterialInput do
  use PhoenixStorybook.Story, :component
  alias LiteCrmWeb.MaterialComponents

  def function, do: &MaterialComponents.material_input/1
  def imports, do: []

  def template do
    """
    <.form :let={f} for={%{}}>
      <.psb-variation-group field={f[:field]}/>
    </.form>
    """
  end

  def variations do
    [
      %VariationGroup{
        id: :filled_style,
        description: "Filled style inputs",
        variations: [
          %Variation{
            id: :filled_empty,
            attributes: %{
              id: "filled-empty",
              name: "filled_empty",
              label: "Filled Input",
              style: :filled
            }
          },
          %Variation{
            id: :filled_with_value,
            attributes: %{
              id: "filled-value",
              name: "filled_value",
              label: "Filled Input with Value",
              style: :filled,
              value: "Sample text"
            }
          },
          %Variation{
            id: :filled_with_helper,
            attributes: %{
              id: "filled-helper",
              name: "filled_helper",
              label: "Filled Input with Helper",
              style: :filled,
              helper: "This is a helper text"
            }
          }
        ]
      },
      %VariationGroup{
        id: :outlined_style,
        description: "Outlined style inputs",
        variations: [
          %Variation{
            id: :outlined_empty,
            attributes: %{
              id: "outlined-empty",
              name: "outlined_empty",
              label: "Outlined Input",
              style: :outlined
            }
          },
          %Variation{
            id: :outlined_with_value,
            attributes: %{
              id: "outlined-value",
              name: "outlined_value",
              label: "Outlined Input with Value",
              style: :outlined,
              value: "Sample text"
            }
          },
          %Variation{
            id: :outlined_with_helper,
            attributes: %{
              id: "outlined-helper",
              name: "outlined_helper",
              label: "Outlined Input with Helper",
              style: :outlined,
              helper: "This is a helper text"
            }
          }
        ]
      },
      %Variation{
        id: :with_name_only,
        description: "Input with name only",
        attributes: %{
          id: "name-only-input",
          name: "name_only",
          label: "Name Only Input",
          style: :outlined
        }
      }
    ]
  end
end
