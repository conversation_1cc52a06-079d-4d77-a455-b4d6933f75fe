defmodule Storybook.MaterialComponents.MenuButton do
  use PhoenixStorybook.Story, :component
  alias LiteCrmWeb.MaterialComponents

  def function, do: &MaterialComponents.menu_button/1
  def imports, do: [{MaterialComponents, []}]

  def variations do
    [
      %Variation{
        id: :default,
        attributes: %{
          id: "default-menu-button"
        },
        slots: [
          "<:button_content>Menu</:button_content>",
          """
          <:menu_contents>
            <ul class="mdc-deprecated-list" role="menu" aria-hidden="true" aria-orientation="vertical">
              <li class="mdc-deprecated-list-item" role="menuitem">
                <span class="mdc-deprecated-list-item__ripple"></span>
                <span class="mdc-deprecated-list-item__text">Option 1</span>
              </li>
              <li class="mdc-deprecated-list-item" role="menuitem">
                <span class="mdc-deprecated-list-item__ripple"></span>
                <span class="mdc-deprecated-list-item__text">Option 2</span>
              </li>
              <li class="mdc-deprecated-list-item" role="menuitem">
                <span class="mdc-deprecated-list-item__ripple"></span>
                <span class="mdc-deprecated-list-item__text">Option 3</span>
              </li>
            </ul>
          </:menu_contents>
          """
        ]
      },
      %Variation{
        id: :with_icons,
        attributes: %{
          id: "menu-button-with-icons"
        },
        slots: [
          "<:button_content>User Menu</:button_content>",
          """
          <:menu_contents>
            <ul class="mdc-deprecated-list" role="menu" aria-hidden="true" aria-orientation="vertical">
              <li class="mdc-deprecated-list-item" role="menuitem">
                <span class="mdc-deprecated-list-item__ripple"></span>
                <i class="material-icons mdc-deprecated-list-item__graphic" aria-hidden="true">person</i>
                <span class="mdc-deprecated-list-item__text">Profile</span>
              </li>
              <li class="mdc-deprecated-list-item" role="menuitem">
                <span class="mdc-deprecated-list-item__ripple"></span>
                <i class="material-icons mdc-deprecated-list-item__graphic" aria-hidden="true">settings</i>
                <span class="mdc-deprecated-list-item__text">Settings</span>
              </li>
              <li class="mdc-deprecated-list-item" role="menuitem">
                <span class="mdc-deprecated-list-item__ripple"></span>
                <i class="material-icons mdc-deprecated-list-item__graphic" aria-hidden="true">logout</i>
                <span class="mdc-deprecated-list-item__text">Logout</span>
              </li>
            </ul>
          </:menu_contents>
          """
        ]
      }
    ]
  end
end
