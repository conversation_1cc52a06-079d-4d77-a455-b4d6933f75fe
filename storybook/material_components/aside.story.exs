defmodule Storybook.MaterialComponents.Aside do
  use PhoenixStorybook.Story, :component
  alias LiteCrmWeb.MaterialComponents

  def function, do: &MaterialComponents.aside/1
  def imports, do: [{MaterialComponents, [sidebar_menu: 1]}]

  def variations do
    [
      %Variation{
        id: :default,
        attributes: %{
          id: "default-aside"
        },
        slots: [
          "<:header><div class=\"text-white font-extrabold tracking-tight\">LiteCRM</div></:header>",
          """
          <:sidebar_menu>
            <.sidebar_menu>
              <:link href="#" current={true} icon="dashboard" id="dashboard">
                Dashboard
              </:link>
              <:link href="#" current={false} icon="move_to_inbox" id="leads">
                Leads
              </:link>
              <:link href="#" current={false} icon="group" id="users">
                Users
              </:link>
            </.sidebar_menu>
          </:sidebar_menu>
          """
        ]
      },
      %Variation{
        id: :disabled,
        attributes: %{
          id: "disabled-aside",
          disabled: true
        },
        slots: [
          "<:header><div class=\"text-white font-extrabold tracking-tight\">LiteCRM</div></:header>",
          """
          <:sidebar_menu>
            <.sidebar_menu>
              <:link href="#" current={true} icon="dashboard" id="dashboard-disabled">
                Dashboard
              </:link>
              <:link href="#" current={false} icon="move_to_inbox" id="leads-disabled">
                Leads
              </:link>
            </.sidebar_menu>
          </:sidebar_menu>
          """
        ]
      }
    ]
  end
end
