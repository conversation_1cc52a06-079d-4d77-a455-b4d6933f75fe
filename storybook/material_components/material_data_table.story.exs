defmodule Storybook.MaterialComponents.MaterialDataTable do
  use PhoenixStorybook.Story, :component
  alias LiteCrmWeb.MaterialComponents

  def function, do: &MaterialComponents.material_data_table/1
  def imports, do: [{MaterialComponents, [aria_sort: 3]}, {Phoenix.Component, [to_form: 1]}]

  def variations do
    # Define the missing variables first
    mock_collection = %{
      entries: [
        %{id: 1, name: "Item 1", description: "Description 1", status: "Active", updated_at: "2023-06-01"},
        %{id: 2, name: "Item 2", description: "Description 2", status: "Inactive", updated_at: "2023-06-02"},
        %{id: 3, name: "Item 3", description: "Description 3", status: "Active", updated_at: "2023-06-03"}
      ],
      page: %{
        limit: 10,
        offset: 0,
        count: 100
      },
      count: 100,
      more?: true
    }

    per_page_form = Phoenix.Component.to_form(%{"per_page" => Integer.to_string(10)})
    page = "1"
    per_page = "10"

    # Now use these variables in your variations
    [
      %Variation{
        id: :default,
        attributes: %{
          id: "default-data-table",
          name: "Default",
          collection: mock_collection,
          page: page,
          per_page: per_page,
          per_page_form: per_page_form,
          order: "id",
          reverse: false
        },
        slots: [
          "<:header label=\"ID\" sort_value={aria_sort(assigns, \"id\", false)} column_id=\"id\" />",
          """
          <:table_body>
            <tr class="mdc-data-table__row">
              <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                  <input
                    type="checkbox"
                    class="mdc-checkbox__native-control"
                    aria-labelledby="1"
                  />
                  <div class="mdc-checkbox__background">
                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                      <path
                        class="mdc-checkbox__checkmark-path"
                        fill="none"
                        d="M1.73,12.91 8.1,19.28 22.79,4.59"
                      />
                    </svg>
                    <div class="mdc-checkbox__mixedmark"></div>
                  </div>
                  <div class="mdc-checkbox__ripple"></div>
                </div>
              </td>
              <th class="mdc-data-table__cell" scope="row">1</th>
              <td class="mdc-data-table__cell">
                <a href="" class="mdc-button">Item 1</a>
              </td>
              <td class="mdc-data-table__cell mdl-data-table__cell--non-numeric">
                <time>2023-06-01</time>
              </td>
            </tr>
          </:table_body>
          """
        ]
      },

      # Other variations...
      %Variation{
        id: :selectable,
        attributes: %{
          id: "selectable-data-table",
          name: "Selectable",
          per_page_form: per_page_form,
          page: page,
          per_page: per_page,
          selectable: true,
          collection: mock_collection,
          order: "id",
          reverse: false,
          all_selected: false,
          some_selected: false
        },
        slots: [
          "<:header label=\"ID\" sort_value={aria_sort(assigns, \"id\", false)} column_id=\"id\" />",
          """
          <:table_body>
            <tr class="mdc-data-table__row">
              <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                <div class="mdc-checkbox mdc-data-table__row-checkbox">
                  <input
                    type="checkbox"
                    class="mdc-checkbox__native-control"
                    aria-labelledby="1"
                  />
                  <div class="mdc-checkbox__background">
                    <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                      <path
                        class="mdc-checkbox__checkmark-path"
                        fill="none"
                        d="M1.73,12.91 8.1,19.28 22.79,4.59"
                      />
                    </svg>
                    <div class="mdc-checkbox__mixedmark"></div>
                  </div>
                  <div class="mdc-checkbox__ripple"></div>
                </div>
              </td>
              <th class="mdc-data-table__cell" scope="row">1</th>
              <td class="mdc-data-table__cell">
                <a href="" class="mdc-button">Item 1</a>
              </td>
              <td class="mdc-data-table__cell mdl-data-table__cell--non-numeric">
                <time>2023-06-01</time>
              </td>
            </tr>
          </:table_body>
          """
        ]
      }
    ]
  end
end
