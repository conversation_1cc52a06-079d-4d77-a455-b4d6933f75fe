defmodule Storybook.MaterialComponents.MaterialSelect do
  use PhoenixStorybook.Story, :component
  alias LiteCrmWeb.MaterialComponents

  def function, do: &MaterialComponents.material_select/1
  def imports, do: []

  def template do
    """
    <div class="p-4 space-y-6">
      <.psb-variation-group/>
    </div>
    """
  end

  def variations do
    [
      %VariationGroup{
        id: :filled_style,
        description: "Filled style selects",
        variations: [
          %Variation{
            id: :filled_basic,
            attributes: %{
              id: "filled-select",
              name: "filled_select",
              label: "Filled Select",
              style: :filled,
              value: ""
            },
            slots: [
              """
              <:option value="option1" label="Option 1" />
              <:option value="option2" label="Option 2" />
              <:option value="option3" label="Option 3" />
              """
            ]
          },
          %Variation{
            id: :filled_with_value,
            attributes: %{
              id: "filled-select-value",
              name: "filled_select_value",
              label: "Filled Select with Value",
              style: :filled,
              value: "option2"
            },
            slots: [
              """
              <:option value="option1" label="Option 1" />
              <:option value="option2" label="Option 2" />
              <:option value="option3" label="Option 3" />
              """
            ]
          },
          %Variation{
            id: :filled_required,
            attributes: %{
              id: "filled-select-required",
              name: "filled_select_required",
              label: "Required Filled Select",
              style: :filled,
              required: true,
              value: ""
            },
            slots: [
              """
              <:option value="option1" label="Option 1" />
              <:option value="option2" label="Option 2" />
              <:option value="option3" label="Option 3" />
              """
            ]
          },
          %Variation{
            id: :filled_with_value_disabled,
            attributes: %{
              id: "filled-select-value_disabled",
              name: "filled_select_value_disabled",
              label: "Disabled Filled Select with Value",
              style: :filled,
              disabled: true,
              value: "option2"
            },
            slots: [
              """
              <:option value="option1" label="Option 1" />
              <:option value="option2" label="Option 2" />
              <:option value="option3" label="Option 3" />
              """
            ]
          }
        ]
      },
      %VariationGroup{
        id: :outlined_style,
        description: "Outlined style selects",
        variations: [
          %Variation{
            id: :outlined_basic,
            attributes: %{
              id: "outlined-select",
              name: "outlined_select",
              label: "Outlined Select",
              style: :outlined,
              value: ""
            },
            slots: [
              """
              <:option value="option1" label="Option 1" />
              <:option value="option2" label="Option 2" />
              <:option value="option3" label="Option 3" />
              """
            ]
          },
          %Variation{
            id: :outlined_with_value,
            attributes: %{
              id: "outlined-select-value",
              name: "outlined_select_value",
              label: "Outlined Select with Value",
              style: :outlined,
              value: "option2"
            },
            slots: [
              """
              <:option value="option1" label="Option 1" />
              <:option value="option2" label="Option 2" />
              <:option value="option3" label="Option 3" />
              """
            ]
          },
          %Variation{
            id: :outlined_disabled,
            attributes: %{
              id: "outlined-select-disabled",
              name: "outlined_select_disabled",
              label: "Disabled Outlined Select",
              style: :outlined,
              disabled: true,
              value: ""
            },
            slots: [
              """
              <:option value="option1" label="Option 1" />
              <:option value="option2" label="Option 2" />
              <:option value="option3" label="Option 3" />
              """
            ]
          }
        ]
      },
      %Variation{
        id: :with_selected_text,
        description: "Select with selected text",
        attributes: %{
          id: "selected-text-select",
          name: "selected_text_select",
          label: "Select with Selected Text",
          style: :outlined,
          value: "option2",
          selected_text: "Option 2"
        },
        slots: [
          """
          <:option value="option1" label="Option 1" />
          <:option value="option2" label="Option 2" />
          <:option value="option3" label="Option 3" />
          """
        ]
      }
    ]
  end
end
