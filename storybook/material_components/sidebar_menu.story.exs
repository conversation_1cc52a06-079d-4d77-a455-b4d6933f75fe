defmodule Storybook.MaterialComponents.SidebarMenu do
  use PhoenixStorybook.Story, :component
  alias LiteCrmWeb.MaterialComponents

  def function, do: &MaterialComponents.sidebar_menu/1
  def imports, do: [{MaterialComponents, []}]

  def variations do
    [
      %Variation{
        id: :default,
        slots: [
          """
          <:link href="#" current={true} icon="dashboard" id="dashboard">
            Dashboard
          </:link>
          <:link href="#" current={false} icon="move_to_inbox" id="leads">
            Leads
          </:link>
          <:link href="#" current={false} icon="group" id="users">
            Users
          </:link>
          <:link href="#" current={false} icon="settings" id="settings">
            Settings
          </:link>
          """
        ]
      },
      %Variation{
        id: :with_active_item,
        slots: [
          """
          <:link href="#" current={false} icon="dashboard" id="dashboard-2">
            Dashboard
          </:link>
          <:link href="#" current={true} icon="move_to_inbox" id="leads-2">
            Leads
          </:link>
          <:link href="#" current={false} icon="group" id="users-2">
            Users
          </:link>
          <:link href="#" current={false} icon="settings" id="settings-2">
            Settings
          </:link>
          """
        ]
      }
    ]
  end
end
