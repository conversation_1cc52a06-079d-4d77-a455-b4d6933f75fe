# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     LiteCrm.Repo.insert!(%LiteCrm.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.

Enum.each(1..100, fn num ->
  LiteCrm.Crm.Lead.create(%{
    description: "Test" <> Integer.to_string(num)
  })
end)
