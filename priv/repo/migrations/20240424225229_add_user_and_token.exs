defmodule LiteCrm.Repo.Migrations.AddUserAndToken do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_sqlite.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:users, primary_key: false) do
      add :hashed_password, :text, null: false
      add :email, :ci_string, null: false
      add :id, :bigserial, null: false, primary_key: true
      add :first_name, :text
      add :last_name, :text
      add :admin, :boolean, null: false, default: false
      add :updated_at, :utc_datetime, null: false
      add :inserted_at, :utc_datetime, null: false
    end

    create unique_index(:users, [:email], name: "users_unique_email_index")

    create table(:tokens, primary_key: false) do
      add :jti, :text, null: false, primary_key: true
      add :subject, :text, null: false
      add :expires_at, :utc_datetime, null: false
      add :purpose, :text, null: false
      add :extra_data, :map
      add :created_at, :utc_datetime, null: false
      add :updated_at, :utc_datetime, null: false
    end
  end

  def down do
    drop table(:tokens)

    drop_if_exists unique_index(:users, [:email], name: "users_unique_email_index")

    drop table(:users)
  end
end
