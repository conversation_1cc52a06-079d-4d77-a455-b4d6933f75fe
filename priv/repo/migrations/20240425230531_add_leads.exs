defmodule LiteCrm.Repo.Migrations.AddLeads do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_sqlite.generate_migrations`
  """

  use Ecto.Migration

  def up do
    create table(:leads, primary_key: false) do
      add :updated_at, :utc_datetime, null: false
      add :inserted_at, :utc_datetime, null: false
      add :description, :text
      add :id, :bigserial, null: false, primary_key: true
    end
  end

  def down do
    drop table(:leads)
  end
end
