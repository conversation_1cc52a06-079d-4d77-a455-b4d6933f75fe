defmodule LiteCrmWeb.UserSessionControllerTest do
  use LiteCrmWeb.ConnCase, async: true

  import LiteCrm.AccountsFixtures

  setup do
    %{
      admin: admin_fixture(),
      user: user_fixture()
    }
  end

  defp admin_fixture do
    user_fixture(%{admin: true, email: "<EMAIL>"})
  end

  describe "impersonation" do
    test "admin can impersonate another user", %{conn: conn, admin: admin, user: user} do
      # Log in as admin
      conn = log_in_user(conn, admin)

      # Impersonate another user
      conn = get(conn, ~p"/impersonate/#{user.id}")

      # Assert that we're redirected to the dashboard
      assert redirected_to(conn) == ~p"/"

      # Follow the redirect
      conn = get(conn, ~p"/")

      # Assert that we're now logged in as the impersonated user
      assert conn.assigns.current_user.id == user.id

      # Assert that the admin user is stored in the session
      assert get_session(conn, :current_admin_user).id == admin.id
    end

    test "non-admin cannot impersonate another user", %{conn: conn, user: user1} do
      # Create another user
      user2 = user_fixture(%{email: "<EMAIL>"})

      # Log in as non-admin user
      conn = log_in_user(conn, user1)

      # Try to impersonate another user
      conn = get(conn, ~p"/impersonate/#{user2.id}")

      # Assert that we're redirected to the home page with an error
      assert redirected_to(conn) == ~p"/"
      assert get_flash(conn, :info) == "Access Denied"

      # Assert that we're still logged in as the original user
      assert conn.assigns.current_user.id == user1.id
    end

    test "admin can resume their session after impersonation", %{
      conn: conn,
      admin: admin,
      user: user
    } do
      # Log in as admin
      conn = log_in_user(conn, admin)

      # Impersonate another user
      conn = get(conn, ~p"/impersonate/#{user.id}")

      # Follow the redirect
      conn = get(conn, ~p"/")

      # Resume admin session
      conn = delete(conn, ~p"/resume")

      # Assert that we're redirected to the dashboard
      assert redirected_to(conn) == ~p"/"

      # Follow the redirect
      conn = get(conn, ~p"/")

      # Assert that we're now logged in as the admin again
      assert conn.assigns.current_user.id == admin.id

      # Assert that the admin user is no longer in the session
      assert get_session(conn, :current_admin_user) == nil
    end
  end

  defp log_in_user(conn, user) do
    token = LiteCrm.Accounts.generate_user_session_token(user)

    conn
    |> Phoenix.ConnTest.init_test_session(%{})
    |> Plug.Conn.put_session(:user_token, token)
  end
end
