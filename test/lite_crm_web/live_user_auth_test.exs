defmodule LiteCrmWeb.LiveUserAuthTest do
  use LiteCrmWeb.ConnCase, async: true
  import Phoenix.LiveViewTest
  import LiteCrm.AccountsFixtures

  alias LiteCrm.Accounts
  alias LiteCrmWeb.LiveUserAuth

  setup do
    %{
      admin: admin_fixture(),
      user: user_fixture()
    }
  end

  defp admin_fixture do
    user_fixture(%{admin: true, email: "<EMAIL>"})
  end

  describe "on_mount" do
    test "assigns current_admin_user to socket when present in session", %{
      conn: conn,
      admin: admin,
      user: user
    } do
      # Log in as admin
      conn = log_in_user(conn, admin)

      # Impersonate another user
      conn = get(conn, ~p"/impersonate/#{user.id}")

      # Follow the redirect
      conn = get(conn, ~p"/")

      # Ensure we have the current_admin_user in the session
      assert get_session(conn, :current_admin_user).id == admin.id

      # Create a LiveView socket with the session
      session = %{"current_admin_user" => get_session(conn, :current_admin_user)}
      socket = %Phoenix.LiveView.Socket{}

      # Call the on_mount callback
      {:cont, socket} = LiveUserAuth.on_mount(:live_user_required, %{}, session, socket)

      # Assert that current_admin_user is assigned to the socket
      assert socket.assigns.current_admin_user.id == admin.id
    end

    test "does not assign current_admin_user when not present in session", %{
      conn: conn,
      user: user
    } do
      # Log in as regular user
      conn = log_in_user(conn, user)

      # Create a LiveView socket with the session
      session = %{}
      socket = %Phoenix.LiveView.Socket{}

      # Call the on_mount callback
      {:cont, socket} = LiveUserAuth.on_mount(:live_user_required, %{}, session, socket)

      # Assert that current_admin_user is not assigned to the socket
      refute Map.has_key?(socket.assigns, :current_admin_user)
    end
  end

  defp log_in_user(conn, user) do
    token = Accounts.generate_user_session_token(user)

    conn
    |> Phoenix.ConnTest.init_test_session(%{})
    |> Plug.Conn.put_session(:user_token, token)
  end
end
