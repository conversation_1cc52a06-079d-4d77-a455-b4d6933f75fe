defmodule LiteCrmWeb.UserAuthTest do
  use LiteCrmWeb.ConnCase, async: true
  import LiteCrm.AccountsFixtures

  alias LiteCrm.Accounts
  alias LiteCrmWeb.UserAuth

  setup do
    %{
      admin: admin_fixture(),
      user: user_fixture()
    }
  end

  defp admin_fixture do
    user_fixture(%{admin: true, email: "<EMAIL>"})
  end

  describe "fetch_current_user/2" do
    test "assigns current_user and current_admin_user to conn", %{
      conn: conn,
      admin: admin,
      user: user
    } do
      # Log in as admin
      conn = log_in_user(conn, admin)

      # Store admin user in session
      conn = Plug.Conn.put_session(conn, :current_admin_user, admin)

      # Call fetch_current_user
      conn = UserAuth.fetch_current_user(conn, [])

      # Assert that current_user and current_admin_user are assigned
      assert conn.assigns.current_user.id == admin.id
      assert conn.assigns.current_admin_user.id == admin.id
    end

    test "assigns only current_user when no admin in session", %{conn: conn, user: user} do
      # Log in as regular user
      conn = log_in_user(conn, user)

      # Call fetch_current_user
      conn = UserAuth.fetch_current_user(conn, [])

      # Assert that only current_user is assigned
      assert conn.assigns.current_user.id == user.id
      assert conn.assigns[:current_admin_user] == nil
    end
  end

  # We need to test the private renew_session function directly
  # Let's create a test that calls it through a public function
  describe "renew_session/1" do
    test "preserves current_admin_user when renewing session", %{conn: conn, admin: admin} do
      # Store admin user in session
      conn =
        conn
        |> Plug.Conn.put_session(:current_admin_user, admin)
        |> Plug.Conn.put_session(:other_key, "value")

      # Call renew_session through log_in_user
      conn = UserAuth.log_in_user(conn, admin)

      # Assert that current_admin_user is preserved but other keys are cleared
      assert get_session(conn, :current_admin_user) != nil
      assert get_session(conn, :other_key) == nil
    end
  end

  describe "log_out_user/1" do
    test "clears the entire session including current_admin_user", %{conn: conn, admin: admin} do
      # Log in as admin
      conn = log_in_user(conn, admin)

      # Store admin user in session
      conn = Plug.Conn.put_session(conn, :current_admin_user, admin)

      # Call log_out_user
      conn = UserAuth.log_out_user(conn)

      # Follow the redirect
      assert redirected_to(conn) == ~p"/"

      # Assert that the session is cleared
      conn = get(recycle(conn), ~p"/")
      assert get_session(conn, :user_token) == nil
      assert get_session(conn, :current_admin_user) == nil
    end
  end

  defp log_in_user(conn, user) do
    token = Accounts.generate_user_session_token(user)

    conn
    |> Phoenix.ConnTest.init_test_session(%{})
    |> Plug.Conn.put_session(:user_token, token)
  end
end
